# `/assets`

The `/assets` directory in the OpenIM repository contains various assets such as images, logos, and animated GIFs. These assets serve different purposes and contribute to the functionality and aesthetics of the OpenIM project.

## Directory Structure:

```bash
assets/
├── README.md                     # Documentation for the assets directory
├── images                        # Directory holding images related to OpenIM
│   ├── architecture.png          # Image depicting the architecture of OpenIM
│   └── mvc.png                   # Image illustrating the Model-View-Controller (MVC) pattern
├── intive-slack.png              # Image displaying the Intive Slack logo
├── logo                          # Directory containing various logo variations for OpenIM
│   ├── openim-logo-black.png     # OpenIM logo with a black background
│   ├── openim-logo-blue.png      # OpenIM logo with a blue background
│   ├── openim-logo-green.png     # OpenIM logo with a green background
│   ├── openim-logo-purple.png    # OpenIM logo with a purple background
│   ├── openim-logo-white.png     # OpenIM logo with a white background
│   ├── openim-logo-yellow.png    # OpenIM logo with a yellow background
│   └── openim-logo.png           # OpenIM logo with a transparent background
└── logo-gif                      # Directory containing animated GIF versions of the OpenIM logo
    └── openim-log.gif            # Animated OpenIM logo with a transparent background
```

## Copyright Notice:

The OpenIM logo, including its variations and animated versions, displayed in this repository [OpenIM](https://github.com/openimsdk/open-im-server) under the `/assets/logo` and `/assets/logo-gif` directories, are protected by copyright laws.

The logo design is credited to @Xx(席欣).

Please respect the intellectual property rights and refrain from unauthorized use and distribution of these assets.