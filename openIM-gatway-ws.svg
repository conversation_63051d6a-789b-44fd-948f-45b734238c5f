<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 276 2694" style="max-width: 276px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9"><style>#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .error-icon{fill:#a44141;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .edge-thickness-normal{stroke-width:1px;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .marker.cross{stroke:lightgrey;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 p{margin:0;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .cluster-label text{fill:#F9FFFE;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .cluster-label span{color:#F9FFFE;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .cluster-label span p{background-color:transparent;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .label text,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 span{fill:#ccc;color:#ccc;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .node rect,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .node circle,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .node ellipse,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .node polygon,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .rough-node .label text,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .node .label text,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .image-shape .label,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .icon-shape .label{text-anchor:middle;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .rough-node .label,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .node .label,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .image-shape .label,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .icon-shape .label{text-align:center;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .node.clickable{cursor:pointer;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .arrowheadPath{fill:lightgrey;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .cluster text{fill:#F9FFFE;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .cluster span{color:#F9FFFE;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 rect.text{fill:none;stroke-width:0;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .icon-shape,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .icon-shape p,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .icon-shape rect,#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M138,62L138,66.167C138,70.333,138,78.667,138,86.333C138,94,138,101,138,104.5L138,108"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M138,166L138,170.167C138,174.333,138,182.667,138,190.333C138,198,138,205,138,208.5L138,212"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M138,270L138,274.167C138,278.333,138,286.667,138,294.333C138,302,138,309,138,312.5L138,316"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M138,374L138,378.167C138,382.333,138,390.667,138,398.333C138,406,138,413,138,416.5L138,420"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M138,478L138,482.167C138,486.333,138,494.667,138,502.333C138,510,138,517,138,520.5L138,524"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M138,582L138,586.167C138,590.333,138,598.667,138,606.333C138,614,138,621,138,624.5L138,628"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_6" d="M138,686L138,690.167C138,694.333,138,702.667,138,710.333C138,718,138,725,138,728.5L138,732"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_7" d="M138,790L138,794.167C138,798.333,138,806.667,138,814.333C138,822,138,829,138,832.5L138,836"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_8" d="M138,894L138,898.167C138,902.333,138,910.667,138,918.333C138,926,138,933,138,936.5L138,940"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_9" d="M138,998L138,1002.167C138,1006.333,138,1014.667,138,1022.333C138,1030,138,1037,138,1040.5L138,1044"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_10" d="M138,1102L138,1106.167C138,1110.333,138,1118.667,138,1126.333C138,1134,138,1141,138,1144.5L138,1148"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_11" d="M138,1206L138,1210.167C138,1214.333,138,1222.667,138,1230.333C138,1238,138,1245,138,1248.5L138,1252"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_12" d="M138,1310L138,1314.167C138,1318.333,138,1326.667,138,1334.333C138,1342,138,1349,138,1352.5L138,1356"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_13" d="M138,1414L138,1418.167C138,1422.333,138,1430.667,138,1438.333C138,1446,138,1453,138,1456.5L138,1460"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P_14" d="M138,1518L138,1522.167C138,1526.333,138,1534.667,138,1542.333C138,1550,138,1557,138,1560.5L138,1564"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Q_15" d="M138,1622L138,1626.167C138,1630.333,138,1638.667,138,1646.333C138,1654,138,1661,138,1664.5L138,1668"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_16" d="M138,1726L138,1730.167C138,1734.333,138,1742.667,138,1750.333C138,1758,138,1765,138,1768.5L138,1772"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_S_17" d="M138,1830L138,1834.167C138,1838.333,138,1846.667,138,1854.333C138,1862,138,1869,138,1872.5L138,1876"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_T_18" d="M138,1934L138,1938.167C138,1942.333,138,1950.667,138,1958.333C138,1966,138,1973,138,1976.5L138,1980"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_U_19" d="M138,2038L138,2042.167C138,2046.333,138,2054.667,138,2062.333C138,2070,138,2077,138,2080.5L138,2084"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_V_20" d="M138,2142L138,2146.167C138,2150.333,138,2158.667,138,2166.333C138,2174,138,2181,138,2184.5L138,2188"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_W_21" d="M138,2246L138,2250.167C138,2254.333,138,2262.667,138,2270.333C138,2278,138,2285,138,2288.5L138,2292"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_X_22" d="M138,2374L138,2378.167C138,2382.333,138,2390.667,138,2398.333C138,2406,138,2413,138,2416.5L138,2420"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_Y_23" d="M138,2478L138,2482.167C138,2486.333,138,2494.667,138,2502.333C138,2510,138,2517,138,2520.5L138,2524"></path><path marker-end="url(#mermaid-c0ef116f-4757-4687-9f6e-b0638a3470f9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Y_Z_24" d="M138,2582L138,2586.167C138,2590.333,138,2598.667,138,2606.333C138,2614,138,2621,138,2624.5L138,2628"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(138, 35)" id="flowchart-A-531" class="node default"><rect height="54" width="154.3046875" y="-27" x="-77.15234375" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-47.15234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="94.3046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>main.go 启动</p></span></div></foreignObject></g></g><g transform="translate(138, 139)" id="flowchart-B-532" class="node default"><rect height="54" width="246.9609375" y="-27" x="-123.48046875" style="" class="basic label-container"></rect><g transform="translate(-93.48046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="186.9609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>cmd.NewMsgGatewayCmd</p></span></div></foreignObject></g></g><g transform="translate(138, 243)" id="flowchart-C-534" class="node default"><rect height="54" width="244.125" y="-27" x="-122.0625" style="" class="basic label-container"></rect><g transform="translate(-92.0625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="184.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建MsgGatewayCmd实例</p></span></div></foreignObject></g></g><g transform="translate(138, 347)" id="flowchart-D-536" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"></rect><g transform="translate(-72, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>配置文件映射初始化</p></span></div></foreignObject></g></g><g transform="translate(138, 451)" id="flowchart-E-538" class="node default"><rect height="54" width="171.9453125" y="-27" x="-85.97265625" style="" class="basic label-container"></rect><g transform="translate(-55.97265625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="111.9453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RootCmd初始化</p></span></div></foreignObject></g></g><g transform="translate(138, 555)" id="flowchart-F-540" class="node default"><rect height="54" width="125.234375" y="-27" x="-62.6171875" style="" class="basic label-container"></rect><g transform="translate(-32.6171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="65.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Exec执行</p></span></div></foreignObject></g></g><g transform="translate(138, 659)" id="flowchart-G-542" class="node default"><rect height="54" width="124.2734375" y="-27" x="-62.13671875" style="" class="basic label-container"></rect><g transform="translate(-32.13671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64.2734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>runE方法</p></span></div></foreignObject></g></g><g transform="translate(138, 763)" id="flowchart-H-544" class="node default"><rect height="54" width="162.8515625" y="-27" x="-81.42578125" style="" class="basic label-container"></rect><g transform="translate(-51.42578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="102.8515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置Index配置</p></span></div></foreignObject></g></g><g transform="translate(138, 867)" id="flowchart-I-546" class="node default"><rect height="54" width="189.7265625" y="-27" x="-94.86328125" style="" class="basic label-container"></rect><g transform="translate(-64.86328125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="129.7265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>startrpc.Start调用</p></span></div></foreignObject></g></g><g transform="translate(138, 971)" id="flowchart-J-548" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>服务发现初始化</p></span></div></foreignObject></g></g><g transform="translate(138, 1075)" id="flowchart-K-550" class="node default"><rect height="54" width="151.8046875" y="-27" x="-75.90234375" style="" class="basic label-container"></rect><g transform="translate(-45.90234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="91.8046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RPC服务注册</p></span></div></foreignObject></g></g><g transform="translate(138, 1179)" id="flowchart-L-552" class="node default"><rect height="54" width="218.4296875" y="-27" x="-109.21484375" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-79.21484375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="158.4296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>msggateway.Start调用</p></span></div></foreignObject></g></g><g transform="translate(138, 1283)" id="flowchart-M-554" class="node default"><rect height="54" width="201.8828125" y="-27" x="-100.94140625" style="" class="basic label-container"></rect><g transform="translate(-70.94140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="141.8828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取WebSocket端口</p></span></div></foreignObject></g></g><g transform="translate(138, 1387)" id="flowchart-N-556" class="node default"><rect height="54" width="177.34375" y="-27" x="-88.671875" style="" class="basic label-container"></rect><g transform="translate(-58.671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="117.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis连接初始化</p></span></div></foreignObject></g></g><g transform="translate(138, 1491)" id="flowchart-O-558" class="node default"><rect height="54" width="189.53125" y="-27" x="-94.765625" style="" class="basic label-container"></rect><g transform="translate(-64.765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="129.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建WsServer实例</p></span></div></foreignObject></g></g><g transform="translate(138, 1595)" id="flowchart-P-560" class="node default"><rect height="54" width="197.546875" y="-27" x="-98.7734375" style="" class="basic label-container"></rect><g transform="translate(-68.7734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="137.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建HubServer实例</p></span></div></foreignObject></g></g><g transform="translate(138, 1699)" id="flowchart-Q-562" class="node default"><rect height="54" width="177.5234375" y="-27" x="-88.76171875" style="" class="basic label-container"></rect><g transform="translate(-58.76171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="117.5234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>InitServer初始化</p></span></div></foreignObject></g></g><g transform="translate(138, 1803)" id="flowchart-R-564" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置服务发现注册</p></span></div></foreignObject></g></g><g transform="translate(138, 1907)" id="flowchart-S-566" class="node default"><rect height="54" width="159.8359375" y="-27" x="-79.91796875" style="" class="basic label-container"></rect><g transform="translate(-49.91796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="99.8359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>注册gRPC服务</p></span></div></foreignObject></g></g><g transform="translate(138, 2011)" id="flowchart-T-568" class="node default"><rect height="54" width="220" y="-27" x="-110" style="" class="basic label-container"></rect><g transform="translate(-80, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>启动在线状态变更监听</p></span></div></foreignObject></g></g><g transform="translate(138, 2115)" id="flowchart-U-570" class="node default"><rect height="54" width="187.609375" y="-27" x="-93.8046875" style="fill:#c8e6c9 !important" class="basic label-container"></rect><g transform="translate(-63.8046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="127.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WsServer.Run启动</p></span></div></foreignObject></g></g><g transform="translate(138, 2219)" id="flowchart-V-572" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"></rect><g transform="translate(-72, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>启动客户端管理协程</p></span></div></foreignObject></g></g><g transform="translate(138, 2335)" id="flowchart-W-574" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>启动HTTP WebSocket服务器</p></span></div></foreignObject></g></g><g transform="translate(138, 2451)" id="flowchart-X-576" class="node default"><rect height="54" width="201.8828125" y="-27" x="-100.94140625" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-70.94140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="141.8828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>监听WebSocket连接</p></span></div></foreignObject></g></g><g transform="translate(138, 2555)" id="flowchart-Y-578" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>处理客户端连接</p></span></div></foreignObject></g></g><g transform="translate(138, 2659)" id="flowchart-Z-580" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>消息处理循环</p></span></div></foreignObject></g></g></g></g></g></svg>
