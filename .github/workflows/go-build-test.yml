name: Go Build Test

on:
  push:
  pull_request:
    paths-ignore:
      - "**/*.md"

  workflow_dispatch:

jobs:
  go-build:
    name: Test with go ${{ matrix.go_version }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}

    env:
      SHARE_CONFIG_PATH: config/share.yml

    permissions:
      contents: write
      pull-requests: write
    strategy:
      matrix:
        os: [ubuntu-latest]
        go_version: ["1.22.x"]

    steps:
      - name: Checkout Server repository
        uses: actions/checkout@v4

      - name: Set up Go ${{ matrix.go_version }}
        uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go_version }}

      - name: Get Server dependencies
        run: |
          go install github.com/magefile/mage@latest
          go mod tidy
          go mod download

      - name: Set up infra services
        uses: hoverkraft-tech/compose-action@v2.0.1
        with:
          compose-file: "./docker-compose.yml"

      - name: Modify Server Configuration
        run: |
          yq e '.secret = 123456' -i ${{ env.SHARE_CONFIG_PATH }}

      # - name: Get Internal IP Address
      #   id: get-ip
      #   run: |
      #     IP=$(hostname -I | awk '{print $1}')
      #     echo "The IP Address is: $IP"
      #     echo "::set-output name=ip::$IP"

      # - name: Update .env
      #   run: |
      #     sed -i 's|externalAddress:.*|externalAddress: "http://${{ steps.get-ip.outputs.ip }}:10005"|' config/minio.yml
      #     cat config/minio.yml

      - name: Build and test Server Services
        run: |
          mage build
          mage start
          mage check

      - name: Checkout Chat repository
        uses: actions/checkout@v4
        with:
          repository: "openimsdk/chat"
          path: "chat-repo"

      - name: Get Chat dependencies
        run: |
          cd ${{ github.workspace }}/chat-repo
          go mod tidy
          go mod download
          go install github.com/magefile/mage@latest

      - name: Modify Chat Configuration
        run: |
          cd ${{ github.workspace }}/chat-repo
          yq e '.openIM.secret = 123456' -i ${{ env.SHARE_CONFIG_PATH }}

      - name: Build and test Chat Services
        run: |
          cd ${{ github.workspace }}/chat-repo
          mage build
          mage start
          mage check

      - name: Test Server and Chat
        run: |
          check_error() {
            echo "Response: $1"
            errCode=$(echo $1 | jq -r '.errCode')
            if [ "$errCode" != "0" ]; then
              errMsg=$(echo $1 | jq -r '.errMsg')
              echo "Error: $errMsg"
              exit 1
            fi
          }

          # Test register
          response1=$(curl -X POST -H "Content-Type: application/json" -H "operationID: imAdmin" -d '{
            "verifyCode": "666666",
            "platform": 3,
            "autoLogin": true,
            "user":{
            "nickname": "test12312",
            "areaCode":"+86",
            "phoneNumber": "***********",
            "password":"test123456"
            }
          }' http://127.0.0.1:10008/account/register)
          check_error "$response1"
          userID1=$(echo $response1 | jq -r '.data.userID')
          echo "userID1: $userID1"

          response2=$(curl -X POST -H "Content-Type: application/json" -H "operationID: imAdmin" -d '{
            "verifyCode": "666666",
            "platform": 3,
            "autoLogin": true,
            "user":{
            "nickname": "test22312",
            "areaCode":"+86",
            "phoneNumber": "***********",
            "password":"test123456"
            }
          }' http://127.0.0.1:10008/account/register)
          check_error "$response2"
          userID2=$(echo $response2 | jq -r '.data.userID')
          echo "userID2: $userID2"

          # Test login
          login_response=$(curl -X POST -H "Content-Type: application/json" -H "operationID: imAdmin"  -d '{
            "platform": 3,
            "areaCode":"+86",
            "phoneNumber": "***********",
            "password":"test123456"
          }' http://localhost:10008/account/login)
          check_error "$login_response"

          # Test get admin token
          get_admin_token_response=$(curl -X POST -H "Content-Type: application/json" -H "operationID: imAdmin" -d '{
            "secret": "123456",
            "platformID": 2,
            "userID": "imAdmin"
          }' http://127.0.0.1:10002/auth/get_admin_token)
          check_error "$get_admin_token_response"
          adminToken=$(echo $get_admin_token_response | jq -r '.data.token')
          echo "adminToken: $adminToken"

          # Test send message
          send_msg_response=$(curl -X POST -H "Content-Type: application/json" -H "operationID: imAdmin" -H "token: $adminToken" -d '{
            "sendID": "'$userID1'",
            "recvID": "'$userID2'",
            "senderPlatformID": 3,
            "content": {
              "content": "hello!!"
            },
            "contentType": 101,
            "sessionType": 1
           }' http://127.0.0.1:10002/msg/send_msg)
           check_error "$send_msg_response"

          # Test get users
          get_users_response=$(curl -X POST -H "Content-Type: application/json" -H "operationID: imAdmin" -H "token: $adminToken" -d '{
            "pagination": {
               "pageNumber": 1,
               "showNumber": 100
             }
            }' http://127.0.0.1:10002/user/get_users)
          check_error "$get_users_response"

  go-test:
    name: Benchmark Test with go ${{ matrix.go_version }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    permissions:
      contents: write
    env:
      SDK_DIR: openim-sdk-core
      NOTIFICATION_CONFIG_PATH: config/notification.yml
      SHARE_CONFIG_PATH: config/share.yml

    strategy:
      matrix:
        os: [ubuntu-latest]
        go_version: ["1.22.x"]

    steps:
      - name: Checkout Server repository
        uses: actions/checkout@v4

      - name: Checkout SDK repository
        uses: actions/checkout@v4
        with:
          repository: "openimsdk/openim-sdk-core"
          ref: "main"
          path: ${{ env.SDK_DIR }}

      - name: Set up Go ${{ matrix.go_version }}
        uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go_version }}

      - name: Get Server dependencies
        run: |
          go install github.com/magefile/mage@latest
          go mod download

      - name: Modify Server Configuration
        run: |
          yq e '.groupCreated.isSendMsg = true' -i ${{ env.NOTIFICATION_CONFIG_PATH }}
          yq e '.friendApplicationApproved.isSendMsg = true' -i ${{ env.NOTIFICATION_CONFIG_PATH }}
          yq e '.secret = 123456' -i ${{ env.SHARE_CONFIG_PATH }}

      - name: Start Server Services
        run: |
          docker compose up -d
          mage build
          mage start
          mage check

      - name: Build test SDK core
        run: |
          cd ${{ env.SDK_DIR }}
          go mod tidy
          cd integration_test
          mkdir data
          go run main.go -lgr 0.8 -imf -crg -ckgn -ckcon -sem -ckmsn -u 20 -su 5 -lg 2 -cg 2 -cgm 3 -sm 10 -gm 10 -reg

  dockerfile-test:
    name: Build and Test Dockerfile
    runs-on: ubuntu-latest
    strategy:
      matrix:
        go_version: ["1.22"]

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Go ${{ matrix.go_version }}
        uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go_version }}

      - name: Get dependencies
        run: |
          go mod tidy
          go mod download
          go install github.com/magefile/mage@latest

      - name: Build Docker Image
        run: |
          IMAGE_NAME="${{ github.event.repository.name }}-test"
          CONTAINER_NAME="${{ github.event.repository.name }}-container"
          docker build -t $IMAGE_NAME .

      - name: Run Docker Container
        run: |
          IMAGE_NAME="${{ github.event.repository.name }}-test"
          CONTAINER_NAME="${{ github.event.repository.name }}-container"
          docker run --name $CONTAINER_NAME -d $IMAGE_NAME
          docker ps -a

      - name: Test Docker Container Logs
        run: |
          CONTAINER_NAME="${{ github.event.repository.name }}-container"
          docker logs $CONTAINER_NAME
