name: Update Version File on Release

on:
  release:
    types: [created]

jobs:
  update-version:
    runs-on: ubuntu-latest
    env:
      TAG_VERSION: ${{ github.event.release.tag_name }} 
    steps:
      # Step 1: Checkout the original repository's code
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # Step 2: Set up Git with official account
      - name: Set up Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      # Step 3: Check and delete existing tag
      - name: Check and delete existing tag
        run: |
          if git rev-parse ${{ env.TAG_VERSION }} >/dev/null 2>&1; then
            git tag -d ${{ env.TAG_VERSION }}
            git push --delete origin ${{ env.TAG_VERSION }}
          fi

      # Step 4: Update version file
      - name: Update version file
        run: |
          echo "${{ env.TAG_VERSION }}" > version/version

      # Step 5: Commit and push changes
      - name: Commit and push changes
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          git add version/version
          git commit -m "Update version to ${{ env.TAG_VERSION }}"
          git push origin HEAD:${{ github.ref }}

      # Step 6: Create and push tag
      - name: Create and push tag
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          git tag ${{ env.TAG_VERSION }}
          git push origin ${{ env.TAG_VERSION }}

      # Step 7: Find and Publish Draft Release
      - name: Find and Publish Draft Release
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            // Get the list of releases
            const releases = await github.rest.repos.listReleases({
              owner: context.repo.owner,
              repo: context.repo.repo
            });

            // Find the draft release where the title and tag_name are the same
            const draftRelease = releases.data.find(release => 
              release.draft && release.name === release.tag_name
            );

            if (draftRelease) {
              // Publish the draft release using the release_id
              await github.rest.repos.updateRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
                release_id: draftRelease.id,  // Use release_id
                draft: false
              });

              core.info(`Draft Release ${draftRelease.tag_name} published successfully.`);
            } else {
              core.info("No matching draft release found.");
            }