name: Publish Docker image to registries

on:
  push:
    branches:
      - release-*
    # tags:
    #   - 'v*'

  release:
    types: [published]

  workflow_dispatch:
    inputs:
      tag:
        description: "Tag version to be used for Docker image"
        required: true
        default: "v3.8.0"

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: main-repo

      # - name: Set up QEMU
      #   uses: docker/setup-qemu-action@v3.3.0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3.8.0

      - name: Build Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: ./main-repo
          load: true
          tags: "openim/openim-server:local"
          cache-from: type=gha,scope=build
          cache-to: type=gha,mode=max,scope=build

      - name: Checkout compose repository
        uses: actions/checkout@v4
        with:
          repository: "openimsdk/openim-docker"
          path: "compose-repo"

      - name: Get Internal IP Address
        id: get-ip
        run: |
          IP=$(hostname -I | awk '{print $1}')
          echo "The IP Address is: $IP"
          echo "::set-output name=ip::$IP"

      - name: Update .env to use the local image
        run: |
          sed -i 's|OPENIM_SERVER_IMAGE=.*|OPENIM_SERVER_IMAGE=openim/openim-server:local|' ${{ github.workspace }}/compose-repo/.env
          sed -i 's|MINIO_EXTERNAL_ADDRESS=.*|MINIO_EXTERNAL_ADDRESS=http://${{ steps.get-ip.outputs.ip }}:10005|' ${{ github.workspace }}/compose-repo/.env

      - name: Start services using Docker Compose
        run: |
          cd ${{ github.workspace }}/compose-repo
          docker compose up -d

          docker compose ps

      - name: Extract metadata for Docker (tags, labels)
        id: meta
        uses: docker/metadata-action@v5.6.0
        with:
          images: |
            openim/openim-server
            ghcr.io/openimsdk/openim-server
            registry.cn-hangzhou.aliyuncs.com/openimsdk/openim-server
          tags: |
            type=ref,event=tag
            type=schedule
            type=ref,event=branch
            # type=semver,pattern={{version}}
            type=semver,pattern=v{{version}}
            type=semver,pattern=release-{{raw}}
            type=sha
            type=raw,value=${{ github.event.inputs.tag }}

      - name: Log in to Docker Hub
        uses: docker/login-action@v3.3.0
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Log in to Aliyun Container Registry
        uses: docker/login-action@v3.3.0
        with:
          registry: registry.cn-hangzhou.aliyuncs.com
          username: ${{ secrets.ALIREGISTRY_USERNAME }}
          password: ${{ secrets.ALIREGISTRY_TOKEN }}

      - name: Push Docker images
        uses: docker/build-push-action@v5
        with:
          context: ./main-repo
          push: true
          platforms: linux/amd64,linux/arm64
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=build
          cache-to: type=gha,mode=max,scope=build

      - name: Verify multi-platform support
        run: |
          images=("openim/openim-server" "ghcr.io/openimsdk/openim-server" "registry.cn-hangzhou.aliyuncs.com/openimsdk/openim-server")
          for image in "${images[@]}"; do
              for tag in $(echo "${{ steps.meta.outputs.tags }}" | tr ',' '\n'); do
                  manifest=$(docker manifest inspect "$image:$tag" || echo "error")
                  if [[ "$manifest" == "error" ]]; then
                      echo "Manifest not found for $image:$tag"
                      exit 1
                  fi
                  amd64_found=$(echo "$manifest" | jq '.manifests[] | select(.platform.architecture == "amd64")')
                  arm64_found=$(echo "$manifest" | jq '.manifests[] | select(.platform.architecture == "arm64")')
                  if [[ -z "$amd64_found" ]]; then
                      echo "Multi-platform support check failed for $image:$tag - missing amd64"
                      exit 1
                  fi
                  if [[ -z "$arm64_found" ]]; then
                      echo "Multi-platform support check failed for $image:$tag - missing arm64"
                      exit 1
                  fi
              done
          done
