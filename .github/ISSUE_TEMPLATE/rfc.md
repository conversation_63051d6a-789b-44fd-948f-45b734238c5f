---
name: RFC - Feature Proposal
about: Submit a proposal for a significant feature to invite community discussion.
title: "[RFC]: "  # Prefix for the title to help identify RFC proposals
labels: rfc, proposal  # Labels to be automatically added
assignees: ''  # Optionally, specify maintainers or teams to be auto-assigned

---

## Proposal Overview
Briefly describe the content and objectives of your proposal.

## Motivation
Why is this new feature necessary? What is the background of this problem?

## Detailed Design
Describe the technical details of the proposal, including implementation steps, code snippets, or architecture diagrams.

## Alternatives Considered
Have other alternatives been considered? Why is this approach preferred over others?

## Impact
How will this proposal affect existing practices and community users?

## Additional Information
Include any other relevant information such as related discussions, prior related work, etc.
