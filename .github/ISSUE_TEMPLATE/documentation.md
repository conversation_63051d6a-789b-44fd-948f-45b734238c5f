---
name: Documentation Update
about: Propose updates to documentation, including README files and other docs.
title: "[DOC]: "  # Prefix for the title to help identify documentation issues
labels: documentation  # Labels to be automatically added
assignees: ''  # Optionally, specify maintainers or teams to be auto-assigned

---

## Documentation Updates
Describe the documentation that needs to be updated or corrected. Please specify the files and sections if possible.

## Motivation
Explain why these updates are necessary. What is missing, misleading, or outdated?

## Suggested Changes
Detail the changes that you propose. If you are suggesting large changes, include examples or mockups of what the updated documentation should look like.

## Additional Information
Include any other information that might be relevant, such as links to discussions or related issues in the repository.
