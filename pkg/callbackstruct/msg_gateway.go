// Copyright © 2023 OpenIM. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package callbackstruct

type CallbackUserOnlineReq struct {
	UserStatusCallbackReq
	// Token           string `json:"token"`
	Seq             int64  `json:"seq"`
	IsAppBackground bool   `json:"isAppBackground"`
	ConnID          string `json:"connID"`
}

type CallbackUserOnlineResp struct {
	CommonCallbackResp
}

type CallbackUserOfflineReq struct {
	UserStatusCallbackReq
	Seq    int64  `json:"seq"`
	ConnID string `json:"connID"`
}

type CallbackUserOfflineResp struct {
	CommonCallbackResp
}

type CallbackUserKickOffReq struct {
	UserStatusCallbackReq
	Seq int64 `json:"seq"`
}

type CallbackUserKickOffResp struct {
	CommonCallbackResp
}
