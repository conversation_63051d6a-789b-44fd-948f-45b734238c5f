// Copyright © 2023 OpenIM. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package callbackstruct

const (
	CallbackBeforeInviteJoinGroupCommand               = "callbackBeforeInviteJoinGroupCommand"
	CallbackAfterJoinGroupCommand                      = "callbackAfterJoinGroupCommand"
	CallbackAfterSetGroupInfoCommand                   = "callbackAfterSetGroupInfoCommand"
	CallbackAfterSetGroupInfoExCommand                 = "callbackAfterSetGroupInfoExCommand"
	CallbackBeforeSetGroupInfoCommand                  = "callbackBeforeSetGroupInfoCommand"
	CallbackBeforeSetGroupInfoExCommand                = "callbackBeforeSetGroupInfoExCommand"
	CallbackAfterRevokeMsgCommand                      = "callbackBeforeAfterMsgCommand"
	CallbackBeforeAddBlackCommand                      = "callbackBeforeAddBlackCommand"
	CallbackAfterAddFriendCommand                      = "callbackAfterAddFriendCommand"
	CallbackBeforeAddFriendAgreeCommand                = "callbackBeforeAddFriendAgreeCommand"
	CallbackAfterAddFriendAgreeCommand                 = "callbackAfterAddFriendAgreeCommand"
	CallbackAfterDeleteFriendCommand                   = "callbackAfterDeleteFriendCommand"
	CallbackBeforeImportFriendsCommand                 = "callbackBeforeImportFriendsCommand"
	CallbackAfterImportFriendsCommand                  = "callbackAfterImportFriendsCommand"
	CallbackAfterRemoveBlackCommand                    = "callbackAfterRemoveBlackCommand"
	CallbackAfterQuitGroupCommand                      = "callbackAfterQuitGroupCommand"
	CallbackAfterKickGroupCommand                      = "callbackAfterKickGroupCommand"
	CallbackAfterDisMissGroupCommand                   = "callbackAfterDisMissGroupCommand"
	CallbackBeforeJoinGroupCommand                     = "callbackBeforeJoinGroupCommand"
	CallbackAfterGroupMsgReadCommand                   = "callbackAfterGroupMsgReadCommand"
	CallbackBeforeMsgModifyCommand                     = "callbackBeforeMsgModifyCommand"
	CallbackAfterUpdateUserInfoCommand                 = "callbackAfterUpdateUserInfoCommand"
	CallbackAfterUpdateUserInfoExCommand               = "callbackAfterUpdateUserInfoExCommand"
	CallbackBeforeUpdateUserInfoExCommand              = "callbackBeforeUpdateUserInfoExCommand"
	CallbackBeforeUserRegisterCommand                  = "callbackBeforeUserRegisterCommand"
	CallbackAfterUserRegisterCommand                   = "callbackAfterUserRegisterCommand"
	CallbackAfterTransferGroupOwnerCommand             = "callbackAfterTransferGroupOwnerCommand"
	CallbackBeforeSetFriendRemarkCommand               = "callbackBeforeSetFriendRemarkCommand"
	CallbackAfterSetFriendRemarkCommand                = "callbackAfterSetFriendRemarkCommand"
	CallbackAfterSingleMsgReadCommand                  = "callbackAfterSingleMsgReadCommand"
	CallbackBeforeSendSingleMsgCommand                 = "callbackBeforeSendSingleMsgCommand"
	CallbackAfterSendSingleMsgCommand                  = "callbackAfterSendSingleMsgCommand"
	CallbackBeforeSendGroupMsgCommand                  = "callbackBeforeSendGroupMsgCommand"
	CallbackAfterSendGroupMsgCommand                   = "callbackAfterSendGroupMsgCommand"
	CallbackAfterUserOnlineCommand                     = "callbackAfterUserOnlineCommand"
	CallbackAfterUserOfflineCommand                    = "callbackAfterUserOfflineCommand"
	CallbackAfterUserKickOffCommand                    = "callbackAfterUserKickOffCommand"
	CallbackBeforeOfflinePushCommand                   = "callbackBeforeOfflinePushCommand"
	CallbackBeforeOnlinePushCommand                    = "callbackBeforeOnlinePushCommand"
	CallbackBeforeGroupOnlinePushCommand               = "callbackBeforeGroupOnlinePushCommand"
	CallbackBeforeAddFriendCommand                     = "callbackBeforeAddFriendCommand"
	CallbackBeforeUpdateUserInfoCommand                = "callbackBeforeUpdateUserInfoCommand"
	CallbackBeforeCreateGroupCommand                   = "callbackBeforeCreateGroupCommand"
	CallbackAfterCreateGroupCommand                    = "callbackAfterCreateGroupCommand"
	CallbackBeforeMembersJoinGroupCommand              = "callbackBeforeMembersJoinGroupCommand"
	CallbackBeforeSetGroupMemberInfoCommand            = "callbackBeforeSetGroupMemberInfoCommand"
	CallbackAfterSetGroupMemberInfoCommand             = "callbackAfterSetGroupMemberInfoCommand"
	CallbackBeforeCreateSingleChatConversationsCommand = "callbackBeforeCreateSingleChatConversationsCommand"
	CallbackAfterCreateSingleChatConversationsCommand  = "callbackAfterCreateSingleChatConversationsCommand"
	CallbackBeforeCreateGroupChatConversationsCommand  = "callbackBeforeCreateGroupChatConversationsCommand"
	CallbackAfterCreateGroupChatConversationsCommand   = "callbackAfterCreateGroupChatConversationsCommand"
)
