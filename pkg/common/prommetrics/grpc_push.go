// Copyright © 2023 OpenIM. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package prommetrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

var (
	MsgOfflinePushFailedCounter = prometheus.NewCounter(prometheus.CounterOpts{
		Name: "msg_offline_push_failed_total",
		Help: "The number of msg failed offline pushed",
	})
	MsgLoneTimePushCounter = prometheus.NewCounter(prometheus.CounterOpts{
		Name: "msg_long_time_push_total",
		Help: "The number of messages with a push time exceeding 10 seconds",
	})
)

func RegistryPush() {
	registry.MustRegister(
		MsgOfflinePushFailedCounter,
		MsgLoneTimePushCounter,
	)
}
