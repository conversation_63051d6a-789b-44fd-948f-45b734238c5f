// Copyright © 2023 OpenIM. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package model

import (
	"time"
)

type FriendRequest struct {
	FromUserID    string    `bson:"from_user_id"`
	ToUserID      string    `bson:"to_user_id"`
	HandleResult  int32     `bson:"handle_result"`
	ReqMsg        string    `bson:"req_msg"`
	CreateTime    time.Time `bson:"create_time"`
	HandlerUserID string    `bson:"handler_user_id"`
	HandleMsg     string    `bson:"handle_msg"`
	HandleTime    time.Time `bson:"handle_time"`
	Ex            string    `bson:"ex"`
}
