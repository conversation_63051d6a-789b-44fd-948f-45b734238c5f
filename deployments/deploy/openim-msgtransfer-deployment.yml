apiVersion: apps/v1
kind: Deployment
metadata:
  name: openim-msgtransfer-server
spec:
  replicas: 2
  selector:
    matchLabels:
      app: openim-msgtransfer-server
  template:
    metadata:
      labels:
        app: openim-msgtransfer-server
    spec:
      containers:
        - name: openim-msgtransfer-container
          image: openim/openim-msgtransfer:v3.8.3
          env:
            - name: CONFIG_PATH
              value: "/config"
            - name: IMENV_REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: openim-redis-secret
                  key: redis-password
            - name: IMENV_MONGODB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-secret
                  key: mongo_openim_username
            - name: IMENV_MONGODB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-secret
                  key: mongo_openim_password
            - name: IMENV_KAFKA_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: openim-kafka-secret
                  key: kafka-password
          volumeMounts:
            - name: openim-config
              mountPath: "/config"
              readOnly: true
          ports:
            - containerPort: 12020
      volumes:
        - name: openim-config
          configMap:
            name: openim-config
