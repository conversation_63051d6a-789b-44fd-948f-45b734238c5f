apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversation-rpc-server
spec:
  replicas: 2
  selector:
    matchLabels:
      app: conversation-rpc-server
  template:
    metadata:
      labels:
        app: conversation-rpc-server
    spec:
      containers:
        - name: conversation-rpc-server-container
          image: openim/openim-rpc-conversation:v3.8.3
          env:
            - name: CONFIG_PATH
              value: "/config"
            - name: IMENV_REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: openim-redis-secret
                  key: redis-password
            - name: IMENV_MONGODB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-secret
                  key: mongo_openim_username
            - name: IMENV_MONGODB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-secret
                  key: mongo_openim_password
          volumeMounts:
            - name: openim-config
              mountPath: "/config"
              readOnly: true
          ports:
            - containerPort: 10220
            - containerPort: 12220
      volumes:
        - name: openim-config
          configMap:
            name: openim-config
