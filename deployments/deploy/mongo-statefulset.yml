apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongo-statefulset
spec:
  serviceName: "mongo"
  replicas: 2
  selector:
    matchLabels:
      app: mongo
  template:
    metadata:
      labels:
        app: mongo
    spec:
      containers:
        - name: mongo
          image: mongo:7.0
          command: ["/bin/bash", "-c"]
          args:
            - >
              docker-entrypoint.sh mongod --wiredTigerCacheSizeGB ${wiredTigerCacheSizeGB} --auth &
              until mongosh -u ${MONGO_INITDB_ROOT_USERNAME} -p ${MONGO_INITDB_ROOT_PASSWORD} --authenticationDatabase admin --eval "db.runCommand({ ping: 1 })" &>/dev/null; do
                echo "Waiting for MongoDB to start...";
                sleep 1;
              done &&
              mongosh -u ${MONGO_INITDB_ROOT_USERNAME} -p ${MONGO_INITDB_ROOT_PASSWORD} --authenticationDatabase admin --eval "
              db = db.getSiblingDB(\"${MONGO_INITDB_DATABASE}\");
              if (!db.getUser(\"${MONGO_OPENIM_USERNAME}\")) {
                db.createUser({
                  user: \"${MONGO_OPENIM_USERNAME}\",
                  pwd: \"${MONGO_OPENIM_PASSWORD}\",
                  roles: [{role: \"readWrite\", db: \"${MONGO_INITDB_DATABASE}\"}]
                });
                print(\"User created successfully: \");
                print(\"Username: ${MONGO_OPENIM_USERNAME}\");
                print(\"Password: ${MONGO_OPENIM_PASSWORD}\");
                print(\"Database: ${MONGO_INITDB_DATABASE}\");
              } else {
                print(\"User already exists in database: ${MONGO_INITDB_DATABASE}, Username: ${MONGO_OPENIM_USERNAME}\");
              }
              " &&
              tail -f /dev/null
          ports:
            - containerPort: 27017
          env:
            - name: MONGO_INITDB_ROOT_USERNAME
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-init-secret
                  key: mongo_initdb_root_username
            - name: MONGO_INITDB_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-init-secret
                  key: mongo_initdb_root_password
            - name: MONGO_INITDB_DATABASE
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-init-secret
                  key: mongo_initdb_database
            - name: MONGO_OPENIM_USERNAME
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-init-secret
                  key: mongo_openim_username
            - name: MONGO_OPENIM_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-init-secret
                  key: mongo_openim_password
            - name: TZ
              value: "Asia/Shanghai"
            - name: wiredTigerCacheSizeGB
              value: "1"
          volumeMounts:
            - name: mongo-storage
              mountPath: /data/db

      volumes:
        - name: mongo-storage
          persistentVolumeClaim:
            claimName: mongo-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongo-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi

---
apiVersion: v1
kind: Secret
metadata:
  name: openim-mongo-init-secret
type: Opaque
data:
  mongo_initdb_root_username: cm9vdA== # base64 for "root"
  mongo_initdb_root_password: b3BlbklNMTIz # base64 for "openIM123"
  mongo_initdb_database: b3BlbmltX3Yz # base64 for "openim_v3"
  mongo_openim_username: b3BlbklN # base64 for "openIM"
  mongo_openim_password: b3BlbklNMTIz # base64 for "openIM123"
