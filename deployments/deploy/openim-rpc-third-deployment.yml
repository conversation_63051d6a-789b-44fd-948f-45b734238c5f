apiVersion: apps/v1
kind: Deployment
metadata:
  name: third-rpc-server
spec:
  replicas: 2
  selector:
    matchLabels:
      app: third-rpc-server
  template:
    metadata:
      labels:
        app: third-rpc-server
    spec:
      containers:
        - name: third-rpc-server-container
          image: openim/openim-rpc-third:v3.8.3
          env:
            - name: CONFIG_PATH
              value: "/config"
            - name: IMENV_MINIO_ACCESSKEYID
              valueFrom:
                secretKeyRef:
                  name: openim-minio-secret
                  key: minio-root-user
            - name: IMENV_MINIO_SECRETACCESSKEY
              valueFrom:
                secretKeyRef:
                  name: openim-minio-secret
                  key: minio-root-password
            - name: IMENV_REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: openim-redis-secret
                  key: redis-password
            - name: IMENV_MONGODB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-secret
                  key: mongo_openim_username
            - name: IMENV_MONGODB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: openim-mongo-secret
                  key: mongo_openim_password
          volumeMounts:
            - name: openim-config
              mountPath: "/config"
              readOnly: true
          ports:
            - containerPort: 10300
            - containerPort: 12300
      volumes:
        - name: openim-config
          configMap:
            name: openim-config
