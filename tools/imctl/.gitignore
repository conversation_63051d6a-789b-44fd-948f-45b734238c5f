# Copyright © 2023 OpenIMSDK.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# ==============================================================================
# For the entire design of.gitignore, ignore git commits and ignore files
#===============================================================================
#

### OpenIM developer supplement ###
logs
.devcontainer
components
out-test
Dockerfile.cross

### Makefile ###
tmp/
bin/
output/
_output/

### OpenIM Config ###
config/config.yaml
./config/config.yaml
.env
./.env

# files used by the developer
.idea.md
.todo.md
.note.md

# ==============================================================================
# Created by https://www.toptal.com/developers/gitignore/api/go,git,vim,tags,test,emacs,backup,jetbrains
# Edit at https://www.toptal.com/developers/gitignore?templates=go,git,vim,tags,test,emacs,backup,jetbrains

cmd/
internal/
pkg/
