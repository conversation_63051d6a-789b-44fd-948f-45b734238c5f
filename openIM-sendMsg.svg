<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 736.9765625 2374" style="max-width: 736.9765625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a"><style>#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .error-icon{fill:#a44141;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .error-text{fill:#ddd;stroke:#ddd;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .edge-thickness-normal{stroke-width:1px;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .edge-thickness-thick{stroke-width:3.5px;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .edge-pattern-solid{stroke-dasharray:0;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .marker.cross{stroke:lightgrey;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a p{margin:0;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .cluster-label text{fill:#F9FFFE;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .cluster-label span{color:#F9FFFE;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .cluster-label span p{background-color:transparent;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .label text,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a span{fill:#ccc;color:#ccc;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .node rect,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .node circle,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .node ellipse,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .node polygon,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .rough-node .label text,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .node .label text,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .image-shape .label,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .icon-shape .label{text-anchor:middle;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .rough-node .label,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .node .label,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .image-shape .label,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .icon-shape .label{text-align:center;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .node.clickable{cursor:pointer;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .arrowheadPath{fill:lightgrey;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .cluster text{fill:#F9FFFE;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .cluster span{color:#F9FFFE;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a rect.text{fill:none;stroke-width:0;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .icon-shape,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .icon-shape p,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .icon-shape rect,#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M273.488,62L273.488,66.167C273.488,70.333,273.488,78.667,273.488,86.333C273.488,94,273.488,101,273.488,104.5L273.488,108"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M273.488,166L273.488,170.167C273.488,174.333,273.488,182.667,273.559,190.417C273.629,198.167,273.769,205.334,273.84,208.917L273.91,212.501"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M244.643,305.154L233.617,316.129C222.591,327.103,200.54,349.051,189.514,365.526C178.488,382,178.488,393,178.488,398.5L178.488,404"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_E_3" d="M303.334,305.154L314.193,316.129C325.052,327.103,346.77,349.051,357.629,365.526C368.488,382,368.488,393,368.488,398.5L368.488,404"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M368.488,462L368.488,466.167C368.488,470.333,368.488,478.667,368.488,486.333C368.488,494,368.488,501,368.488,504.5L368.488,508"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M368.488,566L368.488,570.167C368.488,574.333,368.488,582.667,368.488,590.333C368.488,598,368.488,605,368.488,608.5L368.488,612"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_6" d="M368.488,670L368.488,674.167C368.488,678.333,368.488,686.667,368.559,694.417C368.629,702.167,368.769,709.334,368.84,712.917L368.91,716.501"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_7" d="M316.753,818.265L282.544,833.054C248.334,847.843,179.915,877.422,145.706,897.711C111.496,918,111.496,929,111.496,934.5L111.496,940"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_J_8" d="M368.988,870.5L368.905,876.583C368.822,882.667,368.655,894.833,368.572,906.417C368.488,918,368.488,929,368.488,934.5L368.488,940"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_K_9" d="M421.224,818.265L455.266,833.054C489.309,847.843,557.395,877.422,591.438,897.711C625.48,918,625.48,929,625.48,934.5L625.48,940"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_L_10" d="M111.496,998L111.496,1002.167C111.496,1006.333,111.496,1014.667,111.496,1022.333C111.496,1030,111.496,1037,111.496,1040.5L111.496,1044"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_M_11" d="M368.488,998L368.488,1002.167C368.488,1006.333,368.488,1014.667,368.488,1022.333C368.488,1030,368.488,1037,368.488,1040.5L368.488,1044"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_N_12" d="M625.48,998L625.48,1002.167C625.48,1006.333,625.48,1014.667,625.48,1022.333C625.48,1030,625.48,1037,625.48,1040.5L625.48,1044"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_O_13" d="M111.496,1102L111.496,1106.167C111.496,1110.333,111.496,1118.667,111.496,1126.333C111.496,1134,111.496,1141,111.496,1144.5L111.496,1148"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_P_14" d="M368.488,1102L368.488,1106.167C368.488,1110.333,368.488,1118.667,368.488,1126.333C368.488,1134,368.488,1141,368.488,1144.5L368.488,1148"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_Q_15" d="M625.48,1102L625.48,1106.167C625.48,1110.333,625.48,1118.667,625.48,1126.333C625.48,1134,625.48,1141,625.48,1144.5L625.48,1148"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_R_16" d="M111.496,1206L111.496,1210.167C111.496,1214.333,111.496,1222.667,111.496,1230.333C111.496,1238,111.496,1245,111.496,1248.5L111.496,1252"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_S_17" d="M368.488,1206L368.488,1210.167C368.488,1214.333,368.488,1222.667,368.488,1230.333C368.488,1238,368.488,1245,368.488,1248.5L368.488,1252"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_T_18" d="M625.48,1206L625.48,1210.167C625.48,1214.333,625.48,1222.667,625.48,1230.333C625.48,1238,625.48,1245,625.48,1248.5L625.48,1252"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_U_19" d="M111.496,1310L111.496,1314.167C111.496,1318.333,111.496,1326.667,149.089,1336.387C186.681,1346.107,261.866,1357.214,299.458,1362.768L337.051,1368.321"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_U_20" d="M368.488,1310L368.488,1314.167C368.488,1318.333,368.488,1326.667,375.516,1334.68C382.543,1342.693,396.598,1350.386,403.625,1354.233L410.653,1358.079"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_U_21" d="M625.48,1310L625.48,1314.167C625.48,1318.333,625.48,1326.667,613.135,1334.796C600.79,1342.926,576.099,1350.852,563.754,1354.815L551.408,1358.777"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_V_22" d="M463.488,1414L463.488,1418.167C463.488,1422.333,463.488,1430.667,463.488,1438.333C463.488,1446,463.488,1453,463.488,1456.5L463.488,1460"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_W_23" d="M463.488,1518L463.488,1522.167C463.488,1526.333,463.488,1534.667,463.488,1542.333C463.488,1550,463.488,1557,463.488,1560.5L463.488,1564"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_X_24" d="M463.488,1622L463.488,1626.167C463.488,1630.333,463.488,1638.667,463.488,1646.333C463.488,1654,463.488,1661,463.488,1664.5L463.488,1668"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_Y_25" d="M377.406,1721.147L358.068,1726.123C338.729,1731.098,300.052,1741.049,280.714,1749.525C261.375,1758,261.375,1765,261.375,1768.5L261.375,1772"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Y_Z_26" d="M261.375,1830L261.375,1834.167C261.375,1838.333,261.375,1846.667,261.375,1854.333C261.375,1862,261.375,1869,261.375,1872.5L261.375,1876"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_AA_27" d="M261.375,1934L261.375,1938.167C261.375,1942.333,261.375,1950.667,261.445,1958.417C261.516,1966.167,261.656,1973.334,261.726,1976.917L261.797,1980.501"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_BB_28" d="M221.706,2094.331L206.791,2107.109C191.875,2119.888,162.044,2145.444,147.128,2163.722C132.213,2182,132.213,2193,132.213,2198.5L132.213,2204"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_CC_29" d="M298.655,2097.72L310.405,2109.933C322.155,2122.147,345.654,2146.573,357.404,2164.287C369.154,2182,369.154,2193,369.154,2198.5L369.154,2204"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_DD_30" d="M132.213,2262L132.213,2266.167C132.213,2270.333,132.213,2278.667,132.213,2286.333C132.213,2294,132.213,2301,132.213,2304.5L132.213,2308"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CC_EE_31" d="M369.154,2262L369.154,2266.167C369.154,2270.333,369.154,2278.667,369.154,2286.333C369.154,2294,369.154,2301,369.154,2304.5L369.154,2308"></path><path marker-end="url(#mermaid-8b6cf0f2-9e61-44e9-bf7c-c8cd25e0c24a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_FF_32" d="M480.88,1726L483.564,1730.167C486.248,1734.333,491.616,1742.667,494.3,1750.333C496.984,1758,496.984,1765,496.984,1768.5L496.984,1772"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(178.48828125, 371)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>验证失败</p></span></div></foreignObject></g></g><g transform="translate(368.48828125, 371)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>验证成功</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(111.49609375, 907)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>单聊</p></span></div></foreignObject></g></g><g transform="translate(368.48828125, 907)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>群聊</p></span></div></foreignObject></g></g><g transform="translate(625.48046875, 907)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>通知</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(132.212890625, 2171)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>在线</p></span></div></foreignObject></g></g><g transform="translate(369.154296875, 2171)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>离线</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(273.48828125, 35)" id="flowchart-A-138" class="node default"><rect height="54" width="172" y="-27" x="-86" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>客户端发送消息</p></span></div></foreignObject></g></g><g transform="translate(273.48828125, 139)" id="flowchart-B-139" class="node default"><rect height="54" width="205.203125" y="-27" x="-102.6015625" style="" class="basic label-container"></rect><g transform="translate(-72.6015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="145.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WebSocket Gateway</p></span></div></foreignObject></g></g><g transform="translate(273.48828125, 275)" id="flowchart-C-141" class="node default"><polygon transform="translate(-59,59)" class="label-container" points="59,0 118,-59 59,-118 0,-59"></polygon><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>连接验证</p></span></div></foreignObject></g></g><g transform="translate(178.48828125, 435)" id="flowchart-D-143" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>返回错误</p></span></div></foreignObject></g></g><g transform="translate(368.48828125, 435)" id="flowchart-E-145" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>解析消息数据</p></span></div></foreignObject></g></g><g transform="translate(368.48828125, 539)" id="flowchart-F-147" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>消息格式验证</p></span></div></foreignObject></g></g><g transform="translate(368.48828125, 643)" id="flowchart-G-149" class="node default"><rect height="54" width="214.8203125" y="-27" x="-107.41015625" style="" class="basic label-container"></rect><g transform="translate(-77.41015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="154.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调用Message RPC服务</p></span></div></foreignObject></g></g><g transform="translate(368.48828125, 795)" id="flowchart-H-151" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"></polygon><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>消息类型判断</p></span></div></foreignObject></g></g><g transform="translate(111.49609375, 971)" id="flowchart-I-153" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>单聊消息处理</p></span></div></foreignObject></g></g><g transform="translate(368.48828125, 971)" id="flowchart-J-155" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>群聊消息处理</p></span></div></foreignObject></g></g><g transform="translate(625.48046875, 971)" id="flowchart-K-157" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>通知消息处理</p></span></div></foreignObject></g></g><g transform="translate(111.49609375, 1075)" id="flowchart-L-159" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>权限验证</p></span></div></foreignObject></g></g><g transform="translate(368.48828125, 1075)" id="flowchart-M-161" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>群组权限验证</p></span></div></foreignObject></g></g><g transform="translate(625.48046875, 1075)" id="flowchart-N-163" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>通知权限验证</p></span></div></foreignObject></g></g><g transform="translate(111.49609375, 1179)" id="flowchart-O-165" class="node default"><rect height="54" width="188.5234375" y="-27" x="-94.26171875" style="" class="basic label-container"></rect><g transform="translate(-64.26171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128.5234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Webhook前置回调</p></span></div></foreignObject></g></g><g transform="translate(368.48828125, 1179)" id="flowchart-P-167" class="node default"><rect height="54" width="188.5234375" y="-27" x="-94.26171875" style="" class="basic label-container"></rect><g transform="translate(-64.26171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128.5234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Webhook前置回调</p></span></div></foreignObject></g></g><g transform="translate(625.48046875, 1179)" id="flowchart-Q-169" class="node default"><rect height="54" width="162.1640625" y="-27" x="-81.08203125" style="" class="basic label-container"></rect><g transform="translate(-51.08203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="102.1640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>直接发送到MQ</p></span></div></foreignObject></g></g><g transform="translate(111.49609375, 1283)" id="flowchart-R-171" class="node default"><rect height="54" width="206.9921875" y="-27" x="-103.49609375" style="" class="basic label-container"></rect><g transform="translate(-73.49609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="146.9921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>消息发送到Kafka MQ</p></span></div></foreignObject></g></g><g transform="translate(368.48828125, 1283)" id="flowchart-S-173" class="node default"><rect height="54" width="206.9921875" y="-27" x="-103.49609375" style="" class="basic label-container"></rect><g transform="translate(-73.49609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="146.9921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>消息发送到Kafka MQ</p></span></div></foreignObject></g></g><g transform="translate(625.48046875, 1283)" id="flowchart-T-175" class="node default"><rect height="54" width="206.9921875" y="-27" x="-103.49609375" style="" class="basic label-container"></rect><g transform="translate(-73.49609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="146.9921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>消息发送到Kafka MQ</p></span></div></foreignObject></g></g><g transform="translate(463.48828125, 1387)" id="flowchart-U-177" class="node default"><rect height="54" width="244.9609375" y="-27" x="-122.48046875" style="" class="basic label-container"></rect><g transform="translate(-92.48046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="184.9609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Message Transfer服务消费</p></span></div></foreignObject></g></g><g transform="translate(463.48828125, 1491)" id="flowchart-V-183" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>批量处理消息</p></span></div></foreignObject></g></g><g transform="translate(463.48828125, 1595)" id="flowchart-W-185" class="node default"><rect height="54" width="177.34375" y="-27" x="-88.671875" style="" class="basic label-container"></rect><g transform="translate(-58.671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="117.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>存储到Redis缓存</p></span></div></foreignObject></g></g><g transform="translate(463.48828125, 1699)" id="flowchart-X-187" class="node default"><rect height="54" width="172.1640625" y="-27" x="-86.08203125" style="" class="basic label-container"></rect><g transform="translate(-56.08203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112.1640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>存储到MongoDB</p></span></div></foreignObject></g></g><g transform="translate(261.375, 1803)" id="flowchart-Y-189" class="node default"><rect height="54" width="182.6953125" y="-27" x="-91.34765625" style="" class="basic label-container"></rect><g transform="translate(-61.34765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="122.6953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>发送到Push Topic</p></span></div></foreignObject></g></g><g transform="translate(261.375, 1907)" id="flowchart-Z-191" class="node default"><rect height="54" width="156.8828125" y="-27" x="-78.44140625" style="" class="basic label-container"></rect><g transform="translate(-48.44140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96.8828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Push服务消费</p></span></div></foreignObject></g></g><g transform="translate(261.375, 2059)" id="flowchart-AA-193" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"></polygon><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户在线状态</p></span></div></foreignObject></g></g><g transform="translate(132.212890625, 2235)" id="flowchart-BB-195" class="node default"><rect height="54" width="217.8828125" y="-27" x="-108.94140625" style="" class="basic label-container"></rect><g transform="translate(-78.94140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="157.8828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>实时推送到WebSocket</p></span></div></foreignObject></g></g><g transform="translate(369.154296875, 2235)" id="flowchart-CC-197" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>离线推送处理</p></span></div></foreignObject></g></g><g transform="translate(132.212890625, 2339)" id="flowchart-DD-199" class="node default"><rect height="54" width="172" y="-27" x="-86" style="fill:#c8e6c9 !important" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>客户端接收消息</p></span></div></foreignObject></g></g><g transform="translate(369.154296875, 2339)" id="flowchart-EE-201" class="node default"><rect height="54" width="164.7578125" y="-27" x="-82.37890625" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-52.37890625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="104.7578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>APNs/FCM推送</p></span></div></foreignObject></g></g><g transform="translate(496.984375, 1803)" id="flowchart-FF-203" class="node default"><rect height="54" width="188.5234375" y="-27" x="-94.26171875" style="" class="basic label-container"></rect><g transform="translate(-64.26171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128.5234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Webhook后置回调</p></span></div></foreignObject></g></g></g></g></g></svg>
