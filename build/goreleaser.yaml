# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com

before:
  hooks:
    - make clean
    # You may remove this if you don't use go modules.
    - make tidy
    - make copyright.add
    # you may remove this if you don't need go generate
    - go generate ./...

git:
  # What should be used to sort tags when gathering the current and previous
  # tags if there are more than one tag in the same commit.
  #
  # Default: '-version:refname'
  tag_sort: -version:creatordate

  # What should be used to specify prerelease suffix while sorting tags when gathering
  # the current and previous tags if there are more than one tag in the same commit.
  #
  # Since: v1.17
  prerelease_suffix: "-"

  # Tags to be ignored by GoReleaser.
  # This means that GoReleaser will not pick up tags that match any of the
  # provided values as either previous or current tags.
  #
  # Templates: allowed.
  # Since: v1.21.
  ignore_tags:
    - nightly
    # - "{{.Env.IGNORE_TAG}}"
  
snapshot:
  name_template: "{{ incpatch .Version }}-next"

# gomod:
#   proxy: true

report_sizes: true

# metadata:
#   mod_timestamp: "{{ .CommitTimestamp }}"

builds:
  - binary: openim-api
    id: openim-api
    main: ./cmd/openim-api/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-cmdutils
    id: openim-cmdutils
    main: ./cmd/openim-cmdutils/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-crontask
    id: openim-crontask
    main: ./cmd/openim-crontask/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-msggateway
    id: openim-msggateway
    main: ./cmd/openim-msggateway/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-msgtransfer
    id: openim-msgtransfer
    main: ./cmd/openim-msgtransfer/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-push
    id: openim-push
    main: ./cmd/openim-push/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-rpc-auth
    id: openim-rpc-auth
    main: ./cmd/openim-rpc/openim-rpc-auth/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-rpc-conversation
    id: openim-rpc-conversation
    main: ./cmd/openim-rpc/openim-rpc-conversation/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-rpc-friend
    id: openim-rpc-friend
    main: ./cmd/openim-rpc/openim-rpc-friend/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-rpc-group
    id: openim-rpc-group
    main: ./cmd/openim-rpc/openim-rpc-group/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-rpc-msg
    id: openim-rpc-msg
    main: ./cmd/openim-rpc/openim-rpc-msg/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-rpc-third
    id: openim-rpc-third
    main: ./cmd/openim-rpc/openim-rpc-third/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64

  - binary: openim-rpc-user
    id: openim-rpc-user
    main: ./cmd/openim-rpc/openim-rpc-user/main.go
    goos:
      - darwin
      - windows
      - linux
    goarch:
      - amd64
      - arm64


# TODO：Need a script, such as the init - release to help binary to find the right directory
# ,which can be compiled binary
archives:
  - format: tar.gz
    # this name template makes the OS and Arch compatible with the results of uname.
    name_template: >-
      {{ .ProjectName }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}

    # Set this to true if you want all files in the archive to be in a single directory.
    # If set to true and you extract the archive 'goreleaser_Linux_arm64.tar.gz',
    # you'll get a folder 'goreleaser_Linux_arm64'.
    # If set to false, all files are extracted separately.
    # You can also set it to a custom folder name (templating is supported).
    wrap_in_directory: true

    # use zip for windows archives
    files:
      - CHANGELOG/*
      - deployment/*
      - config/*
      - build/*
      - scripts/*
      - Makefile
      - install.sh
      - docs/*
      - src: "*.md"
        dst: docs

        # Strip parent folders when adding files to the archive.
        strip_parent: true

        # File info.
        # Not all fields are supported by all formats available formats.
        #
        # Default: copied from the source file
        info:
          # Templates: allowed (since v1.14)
          owner: root

          # Templates: allowed (since v1.14)
          group: root

          # Must be in time.RFC3339Nano format.
          #
          # Templates: allowed (since v1.14)
          mtime: "{{ .CommitDate }}"

          # File mode.
          mode: 0644

    format_overrides:
    - goos: windows
      format: zip

changelog:
  sort: asc
  use: github
  filters:
    exclude:
      - "^test:"
      - "^chore"
      - "merge conflict"
      - Merge pull request
      - Merge remote-tracking branch
      - Merge branch
      - go mod tidy
  groups:
    - title: Dependency updates
      regexp: '^.*?(feat|fix)\(deps\)!?:.+$'
      order: 300
    - title: "New Features"
      regexp: '^.*?feat(\([[:word:]]+\))??!?:.+$'
      order: 100
    - title: "Security updates"
      regexp: '^.*?sec(\([[:word:]]+\))??!?:.+$'
      order: 150
    - title: "Bug fixes"
      regexp: '^.*?fix(\([[:word:]]+\))??!?:.+$'
      order: 200
    - title: "Documentation updates"
      regexp: ^.*?doc(\([[:word:]]+\))??!?:.+$
      order: 400
    - title: "Build process updates"
      regexp: ^.*?build(\([[:word:]]+\))??!?:.+$
      order: 400
    - title: Other work
      order: 9999

# dockers:
#   - image_templates:
#       - "openimsdk/open-im-server:{{ .Tag }}-amd64"
#       - "ghcr.io/goreleaser/goreleaser:{{ .Tag }}-amd64"
#     dockerfile: build/images/openim-api/Dockerfile.release
#     ids: 
#       - openim-api
#     use: buildx
#     build_flag_templates:
#       - "--pull"
#       - "--label=io.artifacthub.package.readme-url=https://raw.githubusercontent.com/openimsdk/open-im-server/main/README.md"
#       - "--label=io.artifacthub.package.logo-url=hhttps://github.com/openimsdk/open-im-server/blob/main/assets/logo/openim-logo-green.png"
#       - '--label=io.artifacthub.package.maintainers=[{"name":"Xinwei Xiong","email":"<EMAIL>"}]'
#       - "--label=io.artifacthub.package.license=Apace-2.0"
#       - "--label=org.opencontainers.image.description=OpenIM Open source top instant messaging system"
#       - "--label=org.opencontainers.image.created={{.Date}}"
#       - "--label=org.opencontainers.image.name={{.ProjectName}}"
#       - "--label=org.opencontainers.image.revision={{.FullCommit}}"
#       - "--label=org.opencontainers.image.version={{.Version}}"
#       - "--label=org.opencontainers.image.source={{.GitURL}}"
#       - "--platform=linux/amd64"
#     extra_files:
#       - scripts/entrypoint.sh
#   - image_templates:
#       - "goreleaser/goreleaser:{{ .Tag }}-arm64"
#       - "ghcr.io/goreleaser/goreleaser:{{ .Tag }}-arm64"
#     dockerfile: build/images/openim-api/Dockerfile.release
#     use: buildx
#     build_flag_templates:
#       - "--pull"
#       - "--label=io.artifacthub.package.readme-url=https://raw.githubusercontent.com/openimsdk/open-im-server/main/README.md"
#       - "--label=io.artifacthub.package.logo-url=hhttps://github.com/openimsdk/open-im-server/blob/main/assets/logo/openim-logo-green.png"
#       - '--label=io.artifacthub.package.maintainers=[{"name":"Xinwei Xiong","email":"<EMAIL>"}]'
#       - "--label=io.artifacthub.package.license=Apace-2.0"
#       - "--label=org.opencontainers.image.description=OpenIM Open source top instant messaging system"
#       - "--label=org.opencontainers.image.created={{.Date}}"
#       - "--label=org.opencontainers.image.name={{.ProjectName}}"
#       - "--label=org.opencontainers.image.revision={{.FullCommit}}"
#       - "--label=org.opencontainers.image.version={{.Version}}"
#       - "--label=org.opencontainers.image.source={{.GitURL}}"
#       - "--platform=linux/arm64"
#     goarch: arm64
#     extra_files:
#       - scripts/entrypoint.sh

# docker_manifests:
#   - name_template: "goreleaser/goreleaser:{{ .Tag }}"
#     image_templates:
#       - "goreleaser/goreleaser:{{ .Tag }}-amd64"
#       - "goreleaser/goreleaser:{{ .Tag }}-arm64"
#   - name_template: "ghcr.io/goreleaser/goreleaser:{{ .Tag }}"
#     image_templates:
#       - "ghcr.io/goreleaser/goreleaser:{{ .Tag }}-amd64"
#       - "ghcr.io/goreleaser/goreleaser:{{ .Tag }}-arm64"
#   - name_template: "goreleaser/goreleaser:latest"
#     image_templates:
#       - "goreleaser/goreleaser:{{ .Tag }}-amd64"
#       - "goreleaser/goreleaser:{{ .Tag }}-arm64"
#   - name_template: "ghcr.io/goreleaser/goreleaser:latest"
#     image_templates:
#       - "ghcr.io/goreleaser/goreleaser:{{ .Tag }}-amd64"
#       - "ghcr.io/goreleaser/goreleaser:{{ .Tag }}-arm64"

nfpms:
  - id: packages
    builds:
      - openim-api
      - openim-cmdutils
      - openim-crontask
      - openim-msggateway
      - openim-msgtransfer
      - openim-push
      - openim-rpc-auth
      - openim-rpc-conversation
      - openim-rpc-friend
      - openim-rpc-group
      - openim-rpc-msg
      - openim-rpc-third
      - openim-rpc-user
    # Your app's vendor.
    vendor: OpenIMSDK
    homepage: https://github.com/openimsdk/open-im-server
    maintainer: kubbot <https://github.com/kubbot>
    description: |-
      Auto sync github labels
      kubbot && openimbot
    license: MIT
    formats:
      - apk
      - deb
      - rpm
      - termux.deb # Since: v1.11
      - archlinux # Since: v1.13
    dependencies:
      - git
    recommends:
      - golang


# The lines beneath this are called `modelines`. See `:help modeline`
# Feel free to remove those if you don't want/use them.
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj

# Default: './dist'
dist: ./_output/dist

# .goreleaser.yaml
milestones:
  # You can have multiple milestone configs
  -
    # Repository for the milestone
    # Default is extracted from the origin remote URL
    repo:
      owner: OpenIMSDK
      name: Open-IM-Server

    # Whether to close the milestone
    close: true

    # Fail release on errors, such as missing milestone.
    fail_on_error: false

    # Name of the milestone
    #
    # Default: '{{ .Tag }}'
    name_template: "Current Release"

# publishers:
#   - name: "fury.io"
#     ids:
#       - packages
#     dir: "{{ dir .ArtifactPath }}"
#     cmd: |
#       bash -c '
#       if [[ "{{ .Tag }}" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
#         curl -F package=@{{ .ArtifactName }} https://{{ .Env.FURY_TOKEN }}@push.fury.io/{{ .Env.USERNAME }}/
#       else
#         echo "Skipping deployment: Non-production release detected"
#       fi'

checksum:
  name_template: "{{ .ProjectName }}_checksums.txt"
  algorithm: sha256

release:
  prerelease: auto
