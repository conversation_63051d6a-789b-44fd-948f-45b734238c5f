{{ define "email.to.html" }}
{{ if eq .Status "firing" }}
    {{ range .Alerts }}
    <!-- Begin of OpenIM Alert -->
    <div style="border:1px solid #ccc; padding:10px; margin-bottom:10px;">
        <h3>OpenIM Alert</h3>
        <p><strong>Alert Status:</strong> firing</p>
        <p><strong>Alert Program:</strong> Prometheus Alert</p>
        <p><strong>Severity Level:</strong> {{ .Labels.severity }}</p>
        <p><strong>Alert Type:</strong> {{ .Labels.alertname }}</p>
        <p><strong>Affected Host:</strong> {{ .Labels.instance }}</p>
        <p><strong>Affected Service:</strong> {{ .Labels.job }}</p>
        <p><strong>Alert Subject:</strong> {{ .Annotations.summary }}</p>
        <p><strong>Trigger Time:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05" }}</p>
    </div>
    {{ end }}


{{ else if eq .Status "resolved" }}
    {{ range .Alerts }}
    <!-- Begin of OpenIM Alert -->
    <div style="border:1px solid #ccc; padding:10px; margin-bottom:10px;">
        <h3>OpenIM Alert</h3>
        <p><strong>Alert Status:</strong> resolved</p>
        <p><strong>Alert Program:</strong> Prometheus Alert</p>
        <p><strong>Severity Level:</strong> {{ .Labels.severity }}</p>
        <p><strong>Alert Type:</strong> {{ .Labels.alertname }}</p>
        <p><strong>Affected Host:</strong> {{ .Labels.instance }}</p>
        <p><strong>Affected Service:</strong> {{ .Labels.job }}</p>
        <p><strong>Alert Subject:</strong> {{ .Annotations.summary }}</p>
        <p><strong>Trigger Time:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05" }}</p>
    </div>
    {{ end }}
<!-- End of OpenIM Alert -->
{{ end }}
{{ end }}
