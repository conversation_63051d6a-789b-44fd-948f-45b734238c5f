## Kafka authentication
username:
password:

# Producer acknowledgment settings
producerAck:
# Compression type to use (e.g., none, gzip, snappy)
compressType: none
# List of Kafka broker addresses
address: [localhost:19094]
# Kafka topic for Redis integration
toRedisTopic: toRedis
# Kafka topic for MongoDB integration
toMongoTopic: toMongo
# Kafka topic for push notifications
toPushTopic: toPush
# Kafka topic for offline push notifications
toOfflinePushTopic: toOfflinePush
# Consumer group ID for Redis topic
toRedisGroupID: redis
# Consumer group ID for MongoDB topic
toMongoGroupID: mongo
# Consumer group ID for push notifications topic
toPushGroupID: push
# Consumer group ID for offline push notifications topic
toOfflinePushGroupID: offlinePush
# TLS (Transport Layer Security) configuration
tls:
  # Enable or disable TLS
  enableTLS: false
  # CA certificate file path
  caCrt:
  # Client certificate file path
  clientCrt:
  # Client key file path
  clientKey:
  # Client key password
  clientKeyPwd:
  # Whether to skip TLS verification (not recommended for production)
  insecureSkipVerify: false
