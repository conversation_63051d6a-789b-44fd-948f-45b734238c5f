# URI for database connection, leave empty if using address and credential settings directly
uri: 
# List of MongoDB server addresses
address: [ localhost:37017 ]
# Name of the database
database: openim_v3
# Username for database authentication
username: openIM
# Password for database authentication
password: openIM123
# Authentication source for database authentication, if use root user, set it to admin
authSource: openim_v3
# Maximum number of connections in the connection pool
maxPoolSize: 100
# Maximum number of retry attempts for a failed database connection
maxRetry: 10
