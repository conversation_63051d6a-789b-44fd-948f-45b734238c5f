api:
  # Listening IP; 0.0.0.0 means both internal and external IPs are listened to, default is recommended
  listenIP: 0.0.0.0
  # Listening ports; if multiple are configured, multiple instances will be launched, must be consistent with the number of prometheus.ports
  ports: [ 10002 ]
  # API compression level; 0: default compression, 1: best compression, 2: best speed, -1: no compression
  compressionLevel: 0


prometheus:
  # Whether to enable prometheus
  enable: true
  # autoSetPorts indicates whether to automatically set the ports
  autoSetPorts: true
  # Prometheus listening ports, must match the number of api.ports
  # It will only take effect when autoSetPorts is set to false.
  ports:
  # This address can be accessed via a browser
  grafanaURL:
