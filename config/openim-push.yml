rpc:
  # The IP address where this RPC service registers itself; if left blank, it defaults to the internal network IP
  registerIP:
  # IP address that the RPC service listens on; setting to 0.0.0.0 listens on both internal and external IPs. If left blank, it automatically uses the internal network IP
  listenIP: 0.0.0.0
  # autoSetPorts indicates whether to automatically set the ports
  # if you use in kubernetes, set it to false
  autoSetPorts: true
  # List of ports that the RPC service listens on; configuring multiple ports will launch multiple instances. These must match the number of configured prometheus ports
  # It will only take effect when autoSetPorts is set to false.
  ports:


prometheus:
  # Enable or disable Prometheus monitoring
  enable: true
  # List of ports that Prometheus listens on; these must match the number of rpc.ports to ensure correct monitoring setup
  # It will only take effect when autoSetPorts is set to false.
  ports:

maxConcurrentWorkers: 3
#Use geTui for offline push notifications, or choose fcm or jpns; corresponding configuration settings must be specified.
enable:
getui:
  pushUrl: https://restapi.getui.com/v2/$appId
  masterSecret:
  appKey:
  intent:
  channelID:
  channelName:
fcm:
  # Prioritize using file paths. If the file path is empty, use URL
  filePath:   # File path is concatenated with the parameters passed in through - c(`mage` default pass in `config/`) and filePath.
  authURL:   #  Must start with https or http.
jpush:
  appKey:
  masterSecret:
  pushURL:
  pushIntent:

# iOS system push sound and badge count
iosPush:
  pushSound: xxx
  badgeCount: true
  production: false

fullUserCache: true
