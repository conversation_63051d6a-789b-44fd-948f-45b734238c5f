#!/bin/bash

# OpenIM MsgGateway 快速启动脚本

set -e

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CONFIG_DIR="${PROJECT_ROOT}/config"
OUTPUT_DIR="${PROJECT_ROOT}/_output"

echo "🚀 Starting OpenIM MsgGateway..."
echo "📁 Project Root: ${PROJECT_ROOT}"
echo "⚙️  Config Dir: ${CONFIG_DIR}"

# 检查配置文件是否存在
if [ ! -d "${CONFIG_DIR}" ]; then
    echo "❌ Config directory not found: ${CONFIG_DIR}"
    exit 1
fi

# 检查必要的配置文件
REQUIRED_CONFIGS=(
    "openim-msggateway.yml"
    "share.yml"
    "redis.yml"
    "discovery.yml"
)

for config in "${REQUIRED_CONFIGS[@]}"; do
    if [ ! -f "${CONFIG_DIR}/${config}" ]; then
        echo "❌ Required config file not found: ${CONFIG_DIR}/${config}"
        exit 1
    fi
done

echo "✅ All required config files found"

# 创建输出目录
mkdir -p "${OUTPUT_DIR}"

# 构建
echo "🔨 Building MsgGateway..."
cd "${PROJECT_ROOT}"
go build -o "${OUTPUT_DIR}/openim-msggateway" ./cmd/openim-msggateway

if [ $? -eq 0 ]; then
    echo "✅ Build successful"
else
    echo "❌ Build failed"
    exit 1
fi

# 运行
echo "🎯 Starting MsgGateway..."
"${OUTPUT_DIR}/openim-msggateway" -c "${CONFIG_DIR}" "$@"
