# OpenIM Server Docs

Welcome to the OpenIM Documentation hub! This center provides a comprehensive range of guides and manuals designed to help you get the most out of your OpenIM experience.

## Table of Contents

1. [Contrib](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib) - Guidance on contributing and configurations for developers
2. [Conversions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib) - Coding conventions, logging policies, and other transformation tools


## Contrib

This section offers developers a detailed guide on how to contribute code, set up their environment, and follow the associated processes.

- [Code Conventions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/code-conventions.md) - Rules and conventions for writing code in OpenIM.
- [Development Guide](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/development.md) - A guide on how to carry out development within OpenIM.
- [Git Cherry Pick](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/gitcherry-pick.md) - Guidelines on cherry-picking operations.
- [Git Workflow](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/git-workflow.md) - The git workflow in OpenIM.
- [Initialization Configurations](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/init-config.md) - Guidance on setting up and initializing OpenIM.
- [Docker Installation](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/install-docker.md) - How to install Docker on your machine.
- [Linux Development Environment](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/linux-development.md) - Guide to set up the development environment on Linux.
- [Local Actions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/local-actions.md) - Guidelines on how to carry out certain common actions locally.
- [Offline Deployment](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/offline-deployment.md) - Methods of deploying OpenIM offline.
- [Protoc Tools](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/protoc-tools.md) - Guide on using protoc tools.
- [Go Tools](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/util-go.md) - Tools and libraries in OpenIM for Go.
- [Makefile Tools](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/util-makefile.md) - Best practices and tools for Makefile.
- [Script Tools](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/util-scripts.md) - Best practices and tools for scripts.

## Conversions

This section introduces various conventions and policies within OpenIM, encompassing code, logs, versions, and more.

- [API Conversions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/api.md) - Guidelines and methods for API conversions.
- [Logging Policy](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/bash-log.md) - Logging policies and conventions in OpenIM.
- [CI/CD Actions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/cicd-actions.md) - Procedures and conventions for CI/CD.
- [Commit Conventions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/commit.md) - Conventions for code commits in OpenIM.
- [Directory Conventions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/directory.md) - Directory structure and conventions within OpenIM.
- [Error Codes](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/error-code.md) - List and descriptions of error codes.
- [Go Code Conversions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/go-code.md) - Conventions and conversions for Go code.
- [Docker Image Strategy](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/images.md) - Management strategies for OpenIM Docker images, spanning multiple architectures and image repositories.
- [Logging Conventions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/logging.md) - Further detailed conventions on logging.
- [Version Conventions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/version.md) - Naming and management strategies for OpenIM versions.


## For Developers, Contributors, and Community Maintainers

### Developers & Contributors

If you're a developer or someone keen on contributing:

- Familiarize yourself with our [Code Conventions](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/code-conventions.md) and [Git Workflow](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/git-workflow.md) to ensure smooth contributions.
- Dive into the [Development Guide](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/development.md) to get a hang of the development practices in OpenIM.

### Community Maintainers

As a community maintainer:

- Ensure that contributions align with the standards outlined in our documentation.
- Regularly review the [Logging Policy](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/bash-log.md) and [Error Codes](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/error-code.md) to stay updated.

## For Users

Users should pay particular attention to:

- [Docker Installation](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/install-docker.md) - Necessary if you're planning to use Docker images of OpenIM.
- [Docker Image Strategy](https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/images.md) - To understand the different images available and how to choose the right one for your architecture.