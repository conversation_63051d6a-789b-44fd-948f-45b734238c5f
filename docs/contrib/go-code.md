## OpenIM development specification
We have very high standards for code style and specification, and we want our products to be polished and perfect

## 1. Code style

### 1.1 Code format

- Code must be formatted with `gofmt`.
- Leave spaces between operators and operands.
- It is recommended that a line of code does not exceed 120 characters. If the part exceeds, please use an appropriate line break method. But there are also some exception scenarios, such as import lines, code automatically generated by tools, and struct fields with tags.
- The file length cannot exceed 800 lines.
- Function length cannot exceed 80 lines.
- import specification
- All code must be formatted with `goimports` (it is recommended to set the code Go code editor to: run `goimports` on save).
- Do not use relative paths to import packages, such as `import ../util/net`.
- Import aliases must be used when the package name does not match the last directory name of the import path, or when multiple identical package names conflict.

```go
// bad
"github.com/dgrijalva/jwt-go/v4"

//good
jwt "github.com/dgrijalva/jwt-go/v4"
```
- Imported packages are suggested to be grouped, and anonymous package references use a new group, and anonymous package references are explained.

```go
import (
  // go standard package
  "fmt"
  
  // third party package
  "github.com/jinzhu/gorm"
  "github.com/spf13/cobra"
  "github.com/spf13/viper"
  
  // Anonymous packages are grouped separately, and anonymous package references are explained
  // import mysql driver
  _ "github.com/jinzhu/gorm/dialects/mysql"
  
  // inner package
)
```

### 1.2 Declaration, initialization and definition

When multiple variables need to be used in a function, the `var` declaration can be used at the beginning of the function. Declaration outside the function must use `var`, do not use `:=`, it is easy to step on the scope of the variable.

```go
var (
  Width int
  Height int
)
```

- When initializing a structure reference, please use `&T{}` instead of `new(T)` to make it consistent with structure initialization.

```go
  // bad
  sptr := new(T)
  sptr.Name = "bar"
  
  // good
  sptr := &T{Name: "bar"}
```

- The struct declaration and initialization format takes multiple lines and is defined as follows.

```go
  type User struct{
       Username string
       Email string
  }

  user := User{
  Username: "belm",
  Email: "<EMAIL>",
}
```

- Similar declarations are grouped together, and the same applies to constant, variable, and type declarations.

```go
// bad
import "a"
import "b"

//good
import (
   "a"
   "b"
)
```

- Specify container capacity where possible to pre-allocate memory for the container, for example:

```go
v := make(map[int]string, 4)
v := make([]string, 0, 4)
```

- At the top level, use the standard var keyword. Do not specify a type unless it is different from the type of the expression.

```go
// bad
var s string = F()

func F() string { return "A" }

// good
var s = F()
// Since F already explicitly returns a string type, we don't need to explicitly specify the type of _s
// still of that type

func F() string { return "A" }
```

- This example emphasizes using PascalCase for exported constants and camelCase for unexported ones, avoiding all caps and underscores.

```go
// bad
const (
    MAX_COUNT = 100
    timeout = 30
)

// good
const (
    MaxCount = 100  // Exported constants should use PascalCase.
    defaultTimeout = 30  // Unexported constants should use camelCase.
)
```

- Grouping related constants enhances organization and readability, especially when there are multiple constants related to a particular feature or configuration.

```go
// bad
const apiVersion = "v1"
const retryInterval = 5

// good
const (
    ApiVersion    = "v1"  // Group related constants together for better organization.
    RetryInterval = 5
)
```

- The "good" practice utilizes iota for a clear, concise, and auto-incrementing way to define enumerations, reducing the potential for errors and improving maintainability.

```go
// bad
const (
    StatusActive   = 0
    StatusInactive = 1
    StatusUnknown  = 2
)

// good
const (
    StatusActive = iota  // Use iota for simple and efficient constant enumerations.
    StatusInactive
    StatusUnknown
)
```

- Specifying types explicitly improves clarity, especially when the purpose or type of a constant might not be immediately obvious. Additionally, adding comments to exported constants or those whose purpose isn't clear from the name alone can greatly aid in understanding the code.

```go
// bad
const serverAddress = "localhost:8080"
const debugMode = 1  // Is this supposed to be a boolean or an int?

// good
const ServerAddress string = "localhost:8080"  // Specify type for clarity.
// DebugMode indicates if the application should run in debug mode (true for debug mode).
const DebugMode bool = true
```

- By defining a contextKey type and making userIDKey of this type, you avoid potential collisions with other context keys. This approach leverages Go's type system to provide compile-time checks against misuse.

```go
// bad
const userIDKey = "userID"

// In this example, userIDKey is a string type, which can lead to conflicts or accidental misuse because string keys are prone to typos and collisions in a global namespace.


// good
type contextKey string

const userIDKey contextKey = "userID"
```


- Embedded types (such as mutexes) should be at the top of the field list within the struct, and there must be a blank line separating embedded fields from regular fields.

```go
// bad
type Client struct {
   version int
   http.Client
}

//good
type Client struct {
   http.Client

   version int
}
```

### 1.3 Error Handling

- `error` is returned as the value of the function, `error` must be handled, or the return value assigned to explicitly ignore. For `defer xx.Close()`, there is no need to explicitly handle it.

```go
func load() error {
// normal code
}

// bad
load()

//good
  _ = load()
```

- When `error` is returned as the value of a function and there are multiple return values, `error` must be the last parameter.

```go
// bad
func load() (error, int) {
// normal code
}

//good
func load() (int, error) {
// normal code
}
```

- Perform error handling as early as possible and return as early as possible to reduce nesting.

```go
// bad
if err != nil {
// error code
} else {
// normal code
}

//good
if err != nil {
// error handling
return err
}
// normal code
```

- If you need to use the result of the function call outside if, you should use the following method.

```go
// bad
if v, err := foo(); err != nil {
// error handling
}

// good
v, err := foo()
if err != nil {
// error handling
}
```

- Errors should be judged independently, not combined with other logic.

```go
// bad
v, err := foo()
if err != nil || v == nil {
  // error handling
  return err
}

//good
v, err := foo()
if err != nil {
  // error handling
  return err
}

if v == nil {
  // error handling
  return errors. New("invalid value v")
}
```

- If the return value needs to be initialized, use the following method.

```go
v, err := f()
if err != nil {
  // error handling
  return // or continue.
}
```

- Bug description suggestions
- Error descriptions start with a lowercase letter and do not end with punctuation, for example:

```go
// bad
errors.New("Redis connection failed")
errors.New("redis connection failed.")

// good
errors.New("redis connection failed")
```

- Tell users what they can do, not what they can't.
- When declaring a requirement, use must instead of should. For example, `must be greater than 0, must match regex '[a-z]+'`.
- When declaring that a format is incorrect, use must not. For example, `must not contain`.
- Use may not when declaring an action. For example, `may not be specified when otherField is empty, only name may be specified`.
- When quoting a literal string value, indicate the literal in single quotes. For example, `ust not contain '..'`.
- When referencing another field name, specify that name in backticks. For example, must be greater than `request`.
- When specifying unequal, use words instead of symbols. For example, `must be less than 256, must be greater than or equal to 0 (do not use larger than, bigger than, more than, higher than)`.
- When specifying ranges of numbers, use inclusive ranges whenever possible.
- Go 1.13 or above is recommended, and the error generation method is `fmt.Errorf("module xxx: %w", err)`.

### 1.4 Panic Processing

The use of `panic` should be carefully controlled in Go applications to ensure program stability and predictable error handling. Following are revised guidelines emphasizing the restriction on using `panic` and promoting alternative strategies for error handling and program termination.

- **Prohibited in Business Logic:** Using `panic` within business logic processing is strictly prohibited. Business logic should handle errors gracefully and use error returns to propagate issues up the call stack.

- **Restricted Use in Main Package:** In the main package, the use of `panic` should be reserved for situations where the program is entirely inoperable, such as failure to open essential files, inability to connect to the database, or other critical startup issues. Even in these scenarios, prefer using structured error handling to terminate the program.

- **Prohibition on Exportable Interfaces:** Exportable interfaces must not invoke `panic`. They should handle errors gracefully and return errors as part of their contract.

- **Prefer Errors Over Panic:** It is recommended to use error returns instead of panic to convey errors within a package. This approach promotes error handling that integrates smoothly with Go's error handling idioms.

#### Alternative to Panic: Structured Program Termination

To enforce these guidelines, consider implementing structured functions to terminate the program gracefully in the face of unrecoverable errors, while providing clear error messages. Here are two recommended functions:

```go
// ExitWithError logs an error message and exits the program with a non-zero status.
func ExitWithError(err error) {
	progName := filepath.Base(os.Args[0])
	fmt.Fprintf(os.Stderr, "%s exit -1: %+v\n", progName, err)
	os.Exit(-1)
}

// SIGTERMExit logs a warning message when the program receives a SIGTERM signal and exits with status 0.
func SIGTERMExit() {
	progName := filepath.Base(os.Args[0])
	fmt.Fprintf(os.Stderr, "Warning %s receive process terminal SIGTERM exit 0\n", progName)
}
```

#### Example Usage:

```go
import (
	_ "net/webhook/pprof"

	"github.com/openimsdk/open-im-server/v3/pkg/common/cmd"
	util "github.com/openimsdk/open-im-server/v3/pkg/util/genutil"
)

func main() {
	apiCmd := cmd.NewApiCmd()
	apiCmd.AddPortFlag()
	apiCmd.AddPrometheusPortFlag()
	if err := apiCmd.Execute(); err != nil {
		util.ExitWithError(err)
	}
}
```

In this example, `ExitWithError` is used to terminate the program when an unrecoverable error occurs, providing a clear error message to stderr and exiting with a non-zero status. This approach ensures that critical errors are logged and the program exits in a controlled manner, facilitating troubleshooting and maintaining the stability of the application.


### 1.5 Unit Tests

- The unit test filename naming convention is `example_test.go`.
- Write a test case for every important exportable function.
- Because the functions in the unit test file are not external, the exportable structures, functions, etc. can be uncommented.
- If `func (b *Bar) Foo` exists, the single test function can be `func TestBar_Foo`.

### 1.6 Type assertion failure handling

- A single return value from a type assertion will panic for an incorrect type. Always use the "comma ok" idiom.

```go
// bad
t := n.(int)

//good
t, ok := n.(int)
if !ok {
// error handling
}
```

## 2. Naming convention

The naming convention is a very important part of the code specification. A uniform, short, and precise naming convention can greatly improve the readability of the code and avoid unnecessary bugs.

### 2.1 Package Naming

- The package name must be consistent with the directory name, try to use a meaningful and short package name, and do not conflict with the standard library.
- Package names are all lowercase, without uppercase or underscores, and use multi-level directories to divide the hierarchy.
- Item names can connect multiple words with dashes.
- Do not use plurals for the package name and the directory name where the package is located, for example, `net/url` instead of `net/urls`.
- Don't use broad, meaningless package names like common, util, shared or lib.
- The package name should be simple and clear, such as net, time, log.


### 2.2 Function Naming Conventions

Function names should adhere to the following guidelines, inspired by OpenIM’s standards and Google’s Go Style Guide:

- Use camel case for function names. Start with an uppercase letter for public functions (`MixedCaps`) and a lowercase letter for private functions (`mixedCaps`).
- Exceptions to this rule include code automatically generated by tools (e.g., `xxxx.pb.go`) and test functions that use underscores for clarity (e.g., `TestMyFunction_WhatIsBeingTested`).

### 2.3 File and Directory Naming Practices

To maintain consistency and readability across the OpenIM project, observe the following naming practices:

**File Names:**
- Use underscores (`_`) as the default separator in filenames, keeping them short and descriptive.
- Both hyphens (`-`) and underscores (`_`) are allowed, but underscores are preferred for general use.

**Script and Markdown Files:**
- Prefer hyphens (`-`) for shell scripts and Markdown (`.md`) files to enhance searchability and web compatibility.

**Directories:**
- Name directories with hyphens (`-`) exclusively to separate words, ensuring consistency and readability.

Remember to keep filenames lowercase and use meaningful, concise identifiers to facilitate better organization and navigation within the project.



### 2.4 Structure Naming

- The camel case is adopted, and the first letter is uppercase or lowercase according to the access control, such as `MixedCaps` or `mixedCaps`.
- Struct names should not be verbs, but should be nouns, such as `Node`, `NodeSpec`.
- Avoid using meaningless structure names such as Data and Info.
- The declaration and initialization of the structure should take multiple lines, for example:

```go
// User multi-line declaration
type User struct {
     name string
     Email string
}

// multi-line initialization
u := User{
     UserName: "belm",
     Email: "<EMAIL>",
}
```

### 2.5 Interface Naming

- The interface naming rules are basically consistent with the structure naming rules:
- Interface names of individual functions suffixed with "er"" (e.g. Reader, Writer) can sometimes lead to broken English, but that's okay.
- The interface name of the two functions is named after the two function names, eg ReadWriter.
- An interface name for more than three functions, similar to a structure name.

For example:

```go
// Seeking to an offset before the start of the file is an error.
// Seeking to any positive offset is legal, but the behavior of subsequent
// I/O operations on the underlying object are implementation-dependent.
type Seeker interface {
  Seek(offset int64, whence int) (int64, error)
}

// ReadWriter is the interface that groups the basic Read and Write methods.
type ReadWriter interface {
  reader
  Writer
}
```

### 2.6 Variable Naming

- Variable names must follow camel case, and the initial letter is uppercase or lowercase according to the access control decision.
- In relatively simple (few objects, highly targeted) environments, some names can be abbreviated from full words to single letters, for example:
- user can be abbreviated as u;
- userID can be abbreviated as uid.
- When using proper nouns, the following rules need to be followed:
- If the variable is private and the proper noun is the first word, use lowercase, such as apiClient.
- In other cases, the original wording of the noun should be used, such as APIClient, repoID, UserID.

Some common nouns are listed below.

```go
// A GonicMapper that contains a list of common initialisms taken from golang/lint
var LintGonicMapper = GonicMapper{
     "API": true,
     "ASCII": true,
     "CPU": true,
     "CSS": true,
     "DNS": true,
     "EOF": true,
     "GUID": true,
     "HTML": true,
     "HTTP": true,
     "HTTPS": true,
     "ID": true,
     "IP": true,
     "JSON": true,
     "LHS": true,
     "QPS": true,
     "RAM": true,
     "RHS": true,
     "RPC": true,
     "SLA": true,
     "SMTP": true,
     "SSH": true,
     "TLS": true,
     "TTL": true,
     "UI": true,
     "UID": true,
     "UUID": true,
     "URI": true,
     "URL": true,
     "UTF8": true,
     "VM": true,
     "XML": true,
     "XSRF": true,
     "XSS": true,
}
```

- If the variable type is bool, the name should start with Has, Is, Can or Allow, for example:

```go
var hasConflict bool
var isExist bool
var canManage bool
var allowGitHook bool
```

- Local variables should be as short as possible, for example, use buf to refer to buffer, and use i to refer to index.
- The code automatically generated by the code generation tool can exclude this rule (such as the Id in `xxx.pb.go`)

### 2.7 Constant Naming

In Go, constants play a critical role in defining values that do not change throughout the execution of a program. Adhering to best practices in naming constants can significantly improve the readability and maintainability of your code. Here are some guidelines for constant naming:

- **Camel Case Naming:** The name of a constant must follow the camel case notation. The initial letter should be uppercase or lowercase based on the access control requirements. Uppercase indicates that the constant is exported (visible outside the package), while lowercase indicates package-private visibility (visible only within its own package).

- **Enumeration Type Constants:** For constants that represent a set of enumerated values, it's recommended to define a corresponding type first. This approach not only enhances type safety but also improves code readability by clearly indicating the purpose of the enumeration.

**Example:**

```go
// Code defines an error code type.
type Code int

// Internal errors.
const (
     // ErrUnknown - 0: An unknown error occurred.
     ErrUnknown Code = iota
     // ErrFatal - 1: A fatal error occurred.
     ErrFatal
)
```

In the example above, `Code` is defined as a new type based on `int`. The enumerated constants `ErrUnknown` and `ErrFatal` are then defined with explicit comments to indicate their purpose and values. This pattern is particularly useful for grouping related constants and providing additional context.

### Global Variables and Constants Across Packages

- **Use Constants for Global Variables:** When defining variables that are intended to be accessed across packages, prefer using constants to ensure immutability. This practice avoids unintended modifications to the value, which can lead to unpredictable behavior or hard-to-track bugs.

- **Lowercase for Package-Private Usage:** If a global variable or constant is intended for use only within its own package, it should start with a lowercase letter. This clearly signals its limited scope of visibility, adhering to Go's access control mechanism based on naming conventions.

**Guideline:**

- For global constants that need to be accessed across packages, declare them with an uppercase initial letter. This makes them exported, adhering to Go's visibility rules.
- For constants used within the same package, start their names with a lowercase letter to limit their scope to the package.

**Example:**

```go
package config

// MaxConnections - the maximum number of allowed connections. Visible across packages.
const MaxConnections int = 100

// minIdleTime - the minimum idle time before a connection is considered stale. Only visible within the config package.
const minIdleTime int = 30
```

In this example, `MaxConnections` is a global constant meant to be accessed across packages, hence it starts with an uppercase letter. On the other hand, `minIdleTime` is intended for use only within the `config` package, so it starts with a lowercase letter.

Following these guidelines ensures that your Go code is more readable, maintainable, and consistent with Go's design philosophy and access control mechanisms.

### 2.8 Error naming

- The Error type should be written in the form of FooError.

```go
type ExitError struct {
// ....
}
```

- The Error variable is written in the form of ErrFoo.

```go
var ErrFormat = errors. New("unknown format")
```

For non-standard Err naming, CICD will report an error


### 2.9 Handling Errors Properly

In Go, proper error handling is crucial for creating reliable and maintainable applications. It's important to ensure that errors are not ignored or discarded, as this can lead to unpredictable behavior and difficult-to-debug issues. Here are the guidelines and examples regarding the proper handling of errors.

#### Guideline: Do Not Discard Errors

- **Mandatory Error Propagation:** When calling a function that returns an error, the calling function must handle or propagate the error, instead of ignoring it. This approach ensures that errors are not silently ignored, allowing higher-level logic to make informed decisions about error handling.

#### Incorrect Example: Discarding an Error

```go
package main

import (
	"io/ioutil"
	"log"
)

func ReadFileContent(filename string) string {
	content, _ := ioutil.ReadFile(filename) // Incorrect: Error is ignored
	return string(content)
}

func main() {
	content := ReadFileContent("example.txt")
	log.Println(content)
}
```

In this incorrect example, the error returned by `ioutil.ReadFile` is ignored. This can lead to situations where the program continues execution even if the file doesn't exist or cannot be accessed, potentially causing more cryptic errors downstream.

#### Correct Example: Propagating an Error

```go
package main

import (
	"io/ioutil"
	"log"
)

// ReadFileContent attempts to read and return the content of the specified file.
// It returns an error if reading fails.
func ReadFileContent(filename string) (string, error) {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		// Correct: Propagate the error
		return "", err
	}
	return string(content), nil
}

func main() {
	content, err := ReadFileContent("example.txt")
	if err != nil {
		log.Fatalf("Failed to read file: %v", err)
	}
	log.Println(content)
}
```

In the correct example, the error returned by `ioutil.ReadFile` is propagated back to the caller. The `main` function then checks the error and terminates the program with an appropriate error message if an error occurred. This approach ensures that errors are handled appropriately, and the program does not proceed with invalid state.

### Best Practices for Error Handling

1. **Always check the error returned by a function.** Do not ignore it.
2. **Propagate errors up the call stack unless they can be handled gracefully at the current level.**
3. **Provide context for errors when propagating them, making it easier to trace the source of the error.** This can be achieved using `fmt.Errorf` with the `%w` verb or dedicated wrapping functions provided by some error handling packages.
4. **Log the error at the point where it is handled or makes the program to terminate, to provide insight into the failure.**

By following these guidelines, you ensure that your Go applications handle errors in a consistent and effective manner, improving their reliability and maintainability.


### 2.10 Using Context with IO or Inter-Process Communication (IPC)

In Go, `context.Context` is a powerful construct for managing deadlines, cancellation signals, and other request-scoped values across API boundaries and between processes. It is particularly important in I/O operations or inter-process communication (IPC), where operations might need to be cancelled or timed out.

#### Guideline: Use Context for IO and IPC

- **Mandatory Use of Context:** When performing I/O operations or inter-process communication, it's crucial to use `context.Context` to manage the lifecycle of these operations. This includes setting deadlines, handling cancellation signals, and passing request-scoped values.

#### Incorrect Example: Ignoring Context in an HTTP Call

```go
package main

import (
	"io/ioutil"
	"net/http"
	"log"
)

// FetchData makes an HTTP GET request to the specified URL and returns the response body.
// This function does not use context, making it impossible to cancel the request or set a deadline.
func FetchData(url string) (string, error) {
	resp, err := http.Get(url) // Incorrect: Ignoring context
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

func main() {
	data, err := FetchData("http://example.com")
	if err != nil {
		log.Fatalf("Failed to fetch data: %v", err)
	}
	log.Println(data)
}
```

In this incorrect example, the `FetchData` function makes an HTTP GET request without using a `context`. This approach does not allow the request to be cancelled or a timeout to be set, potentially leading to resources being wasted if the server takes too long to respond or if the operation needs to be aborted for any reason.

#### Correct Example: Using Context in an HTTP Call

```go
package main

import (
	"context"
	"io/ioutil"
	"net/http"
	"log"
	"time"
)

// FetchDataWithContext makes an HTTP GET request to the specified URL using the provided context.
// This allows the request to be cancelled or timed out according to the context's deadline.
func FetchDataWithContext(ctx context.Context, url string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return "", err
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

func main() {
	// Create a context with a 5-second timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	data, err := FetchDataWithContext(ctx, "http://example.com")
	if err != nil {
		log.Fatalf("Failed to fetch data: %v", err)
	}
	log.Println(data)
}
```

In the correct example, `FetchDataWithContext` uses a context to make the HTTP GET request. This allows the operation to be cancelled or subjected to a timeout, as dictated by the context passed to it. The `context.WithTimeout` function is used in `main` to create a context that cancels the request if it takes longer than 5 seconds, demonstrating a practical use of context to manage operation lifecycle.

### Best Practices for Using Context

1. **Pass context as the first parameter of a function**, following the convention `func(ctx context.Context, ...)`.
2. **Never ignore the context** provided to you in functions that support it. Always use it in your I/O or IPC operations.
3. **Avoid storing context in a struct**. Contexts are meant to be passed around within the call stack, not stored.
4. **Use context's cancellation and deadline features** to control the lifecycle of blocking operations, especially in network I/O and IPC scenarios.
5. **Propagate context down the call stack** to any function that supports it, ensuring that your application can respond to cancellation signals and deadlines effectively.

By adhering to these guidelines and examples, you can ensure that your Go applications handle I/O and IPC operations more reliably and efficiently, with proper support for cancellation, timeouts, and request-scoped values.


## 3. Comment specification

- Each exportable name must have a comment, which briefly introduces the exported variables, functions, structures, interfaces, etc.
- All single-line comments are used, and multi-line comments are prohibited.
- Same as the code specification, single-line comments should not be too long, and no more than 120 characters are allowed. If it exceeds, please use a new line to display, and try to keep the format elegant.
- A comment must be a complete sentence, starting with the content to be commented and ending with a period, `the format is // name description.`. For example:

```go
// bad
// logs the flags in the flagset.
func PrintFlags(flags *pflag. FlagSet) {
// normal code
}

//good
// PrintFlags logs the flags in the flagset.
func PrintFlags(flags *pflag. FlagSet) {
// normal code
}
```

- All commented out code should be deleted before submitting code review, otherwise, it should explain why it is not deleted, and give follow-up processing suggestions.

- Multiple comments can be separated by blank lines, as follows:

```go
// Package superman implements methods for saving the world.
//
// Experience has shown that a small number of procedures can prove
// helpful when attempting to save the world.
package superman
```

### 3.1 Package Notes

- Each package has one and only one package-level annotation.
- Package comments are uniformly commented with // in the format of `// Package <package name> package description`, for example:

```go
// Package genericclioptions contains flags which can be added to you command, bound, completed, and produce
// useful helper functions.
package genericclioptions
```

### 3.2 Variable/Constant Comments

- Each variable/constant that can be exported must have a comment description, `the format is // variable name variable description`, for example:

```go
// ErrSigningMethod defines invalid signing method error.
var ErrSigningMethod = errors. New("Invalid signing method")
```

- When there is a large block of constant or variable definition, you can comment a general description in front, and then comment the definition of the constant in detail before or at the end of each line of constant, for example:
```go
// Code must start with 1xxxxx.
const (
     // ErrSuccess - 200: OK.
     ErrSuccess int = iota + 100001

     // ErrUnknown - 500: Internal server error.
     ErrUnknown

     // ErrBind - 400: Error occurred while binding the request body to the struct.
     ErrBind

     // ErrValidation - 400: Validation failed.
     ErrValidation
)
```
### 3.3 Structure Annotation

- Each structure or interface that needs to be exported must have a comment description, the format is `// structure name structure description.`.
- The name of the exportable member variable in the structure, if the meaning is not clear, a comment must be given and placed before the member variable or at the end of the same line. For example:

```go
// User represents a user restful resource. It is also used as gorm model.
type User struct {
     // Standard object's metadata.
     metav1.ObjectMeta `json:"metadata,omitempty"`

     Nickname string `json:"nickname" gorm:"column:nickname"`
     Password string `json:"password" gorm:"column:password"`
     Email string `json:"email" gorm:"column:email"`
     Phone string `json:"phone" gorm:"column:phone"`
     IsAdmin int `json:"isAdmin,omitempty" gorm:"column:isAdmin"`
}
```

### 3.4 Method Notes

Each function or method that needs to be exported must have a comment, the format is // function name function description., for examplelike:

```go
// BeforeUpdate run before update database record.
func (p *Policy) BeforeUpdate() (err error) {
// normal code
  return nil
}
```

### 3.5 Type annotations

- Each type definition and type alias that needs to be exported must have a comment description, the format is `// type name type description.`, for example:

```go
// Code defines an error code type.
type Code int
```

## 4. Type

### 4.1 Strings

- Empty string judgment.

```go
// bad
if s == "" {
     // normal code
}

//good
if len(s) == 0 {
     // normal code
}
```

- `[]byte`/`string` equality comparison.

```go
// bad
var s1 []byte
var s2 []byte
...
bytes.Equal(s1, s2) == 0
bytes.Equal(s1, s2) != 0

//good
var s1 []byte
var s2 []byte
...
bytes. Compare(s1, s2) == 0
bytes. Compare(s1, s2) != 0
```

- Complex strings use raw strings to avoid character escaping.

```go
// bad
regexp.MustCompile("\\.")

//good
regexp.MustCompile(`\.`)
```

### 4.2 Slicing

- Empty slice judgment.

```go
// bad
if len(slice) = 0 {
     // normal code
}

//good
if slice != nil && len(slice) == 0 {
     // normal code
}
```

The above judgment also applies to map and channel.

- Declare a slice.

```go
// bad
s := []string{}
s := make([]string, 0)

//good
var s[]string
```

- slice copy.

```go
// bad
var b1, b2 []byte
for i, v := range b1 {
    b2[i] = v
}
for i := range b1 {
    b2[i] = b1[i]
}

//good
copy(b2, b1)
```

- slice added.

```go
// bad
var a, b []int
for _, v := range a {
     b = append(b, v)
}

//good
var a, b []int
b = append(b, a...)
```

### 4.3 Structure

- struct initialization.

The struct is initialized in multi-line format.

```go
type user struct {
Id int64
name string
}

u1 := user{100, "Colin"}

u2 := user{
     Id: 200,
     Name: "Lex",
}
```

## 5. Control Structure

### 5.1 if

- if accepts the initialization statement, the convention is to create local variables in the following way.

```go
if err := loadConfig(); err != nil {
// error handling
return err
}
```

- if For variables of bool type, true and false judgments should be made directly.

```go
var isAllow bool
if isAllow {
// normal code
}
```

### 5.2 for

- Create local variables using short declarations.

```go
sum := 0
for i := 0; i < 10; i++ {
     sum += 1
}
```

- Don't use defer in for loop, defer will only be executed when the function exits.

```go
// bad
for file := range files {
    fd, err := os. Open(file)
    if err != nil {
    return err
}
defer fd. Close()
// normal code
}

//good
for file := range files {
    func() {
        fd, err := os. Open(file)
        if err != nil {
        return err
    }
    defer fd. Close()
    // normal code
    }()
}
```

### 5.3 range

- If only the first item (key) is needed, discard the second.

```go
for keyIndex := range keys {
// normal code
}
```

- If only the second item is required, underline the first item.

```go
sum := 0
for _, value := range array {
     sum += value
}
```

### 5.4 switch

- must have default.

```go
switch os := runtime.GOOS; os {
     case "linux":
         fmt.Println("Linux.")
     case "darwin":
         fmt.Println("OS X.")
     default:
         fmt.Printf("%s.\n", os)
}
```

### 5.5 goto
- Business code prohibits the use of goto.
- Try not to use frameworks or other low-level source code.

## 6. Functions

- Incoming variables and return variables start with a lowercase letter.
- The number of function parameters cannot exceed 5.
- Function grouping and ordering
- Functions should be sorted in rough calling order.
- Functions in the same file should be grouped by receiver.
- Try to use value transfer instead of pointer transfer.
- The incoming parameters are map, slice, chan, interface, do not pass pointers.

### 6.1 Function parameters

- If the function returns two or three arguments of the same type, or if the meaning of the result is not clear from the context, use named returns, otherwise it is not recommended to use named returns, for example:

```go
func coordinate() (x, y float64, err error) {
// normal code
}
```
- Both incoming and returned variables start with a lowercase letter.
- Try to pass by value instead of pointer.
- The number of parameters cannot exceed 5.
- Multiple return values can return up to three, and if there are more than three, please use struct.

### 6.2 defer

- When resources are created, resources should be released immediately after defer (defer can be used boldly, the performance of defer is greatly improved in Go1.14 version, and the performance loss of defer can be ignored even in performance-sensitive businesses).
- First judge whether there is an error, and then defer to release resources, for example:

```go
rep, err := http. Get(url)
if err != nil {
     return err
}

defer resp.Body.Close()
```

### 6.3 Method Receiver

- It is recommended to use the lowercase of the first English letter of the class name as the name of the receiver.
- Don't use a single character in the name of the receiver when the function exceeds 20 lines.
- The name of the receiver cannot use confusing names such as me, this, and self.

### 6.4 Nesting
- The nesting depth cannot exceed 4 levels.

### 6.5 Variable Naming
- The variable declaration should be placed before the first use of the variable as far as possible, following the principle of proximity.
- If the magic number appears more than twice, it is forbidden to use it and use a constant instead, for example:

```go
// PI...
const Price = 3.14

func getAppleCost(n float64) float64 {
return Price * n
}

func getOrangeCost(n float64) float64 {
return Price * n
}
```

## 7. GOPATH setting specification
- After Go 1.11, the GOPATH rule has been weakened. Existing code (many libraries must have been created before 1.11) must conform to this rule. It is recommended to keep the GOPATH rule to facilitate code maintenance.
- Only one GOPATH is recommended, multiple GOPATHs are not recommended. If multiple GOPATHs are used, the bin directory where compilation takes effect is under the first GOPATH.

## 8. Dependency Management

- Go 1.11 and above must use Go Modules.
- When using Go Modules as a dependency management project, it is not recommended to submit the vendor directory.
- When using Go Modules as a dependency management project, the go.sum file must be submitted.

### 9. Best Practices

- Minimize the use of global variables, but pass parameters, so that each function is "stateless". This reduces coupling and facilitates division of labor and unit testing.
- Verify interface compliance at compile time, for example:

```go
type LogHandler struct {
   h http.Handler
   log *zap. Logger
}
var_http.Handler = LogHandler{}
```

- When the server processes a request, it should create a context, save the relevant information of the request (such as requestID), and pass it in the function call chain.

### 9.1 Performance
- string represents an immutable string variable, modifying string is a relatively heavy operation, and basically needs to re-apply for memory. Therefore, if there is no special need, use []byte more when you need to modify.
- Prefer strconv over fmt.

### 9.2 Precautions

- append Be careful about automatically allocating memory, append may return a newly allocated address.
- If you want to directly modify the value of the map, the value can only be a pointer, otherwise the original value must be overwritten.
- map needs to be locked during concurrency.
- The conversion of interface{} cannot be checked during compilation, it can only be checked at runtime, be careful to cause panic.

## 10 Golang CI Lint

- Golang CI Lint is a fast Go linters runner. It runs linters in parallel, uses caching, and works well with all environments, including CI.

**In local development, you can use the following command to install Golang CI Lint: **

```bash
make lint
```

**In CI/CD, Check the Github Actions status code below after you submit the code directly**

[![OpenIM golangci-lint](https://github.com/openimsdk/open-im-server/actions/workflows/golangci-lint.yml/badge.svg)](https://github.com/openimsdk/open-im-server/actions/workflows/golangci-lint.yml)

golangci lint can select the types of tools, refer to the official documentation: [https://golangci-lint.run/usage/linters/](https://golangci-lint.run/usage/linters/)

The types of comments we currently use include: [https://github.com/openimsdk/open-im-server/blob/main/.golangci.yml](https://github.com/openimsdk/open-im-server/blob/main/.golangci.yml) the `linters.enable` field in the file.

e.g:
```yaml
linters:
  # please, do not use `enable-all`: it's deprecated and will be removed soon.
  # inverted configuration with `enable-all` and `disable` is not scalable during updates of golangci-lint
  # enable-all: true
  disable-all: true
  enable:
    - typecheck     # Basic type checking
    - gofmt         # Format check
    - govet         # Go's standard linting tool
    - gosimple      # Suggestions for simplifying code
    - misspell      # Spelling mistakes
    - staticcheck   # Static analysis
    - unused        # Checks for unused code
    - goimports     # Checks if imports are correctly sorted and formatted
    - godot         # Checks for comment punctuation
    - bodyclose     # Ensures HTTP response body is closed
    - errcheck      # Checks for missed error returns
  fast: true
```

Add that Chinese comments are not allowed in go code, please write a complete golangci lint specification on the basis of the above.


### 10.1 Configuration Document

This configuration document is designed to configure the operational parameters of OpenIM (a hypothetical or specific code analysis tool), customize output formats, and provide detailed settings for specific code checkers (linters). Below is a summary of the document drafted based on the provided configuration information.

#### 10.1 Runtime Options

- **Concurrency** (`concurrency`): Default to use the available CPU count, can be manually set to 4 for parallel analysis.
- **Timeout** (`timeout`): Timeout duration for analysis operations, default is 1 minute, set here to 5 minutes.
- **Issue Exit Code** (`issues-exit-code`): Exit code defaults to 1 if at least one issue is found.
- **Test Files** (`tests`): Whether to include test files, defaults to true.
- **Build Tags** (`build-tags`): Specify build tags used by all linters, defaults to an empty list. Example adds `mytag`.
- **Skip Directories** (`skip-dirs`): Configure which directories' issues are not reported, defaults to empty, but some default directories are independently skipped.
- **Skip Files** (`skip-files`): Specify files where issues should not be reported, supports regular expressions.

#### 10.2 Output Configuration

- **Format** (`format`): Set output format, default is "colored-line-number".
- **Print Issued Lines** (`print-issued-lines`): Whether to print the lines where issues occur, defaults to true.
- **Print Linter Name** (`print-linter-name`): Whether to print the linter name at the end of issue text, defaults to true.
- **Uniqueness Filter** (`uniq-by-line`): Whether to make issue outputs unique per line, defaults to true.
- **Path Prefix** (`path-prefix`): Prefix to add to output file references, defaults to no prefix.
- **Sort Results** (`sort-results`): Sort results by file path, line number, and column number.

#### 10.3 Linters Settings

In the configuration file, the `linters-settings` section allows detailed configuration of individual linters. Below are examples of specific linters settings and their purposes:

- **bidichk**: Used to check bidirectional text characters, ensuring correct display direction of text, especially when dealing with mixed left-to-right (LTR) and right-to-left (RTL) text.
  
- **dogsled**: Monitors excessive use of blank identifiers (`_`) in assignment operations, which may obscure data processing errors or unclear logic.
  
- **dupl**: Identifies duplicate code blocks, helping developers avoid code redundancy. The `threshold` parameter in settings allows adjustment of code similarity threshold triggering warnings.
  
- **errcheck**: Checks for unhandled errors. In Go, error handling is achieved by checking function return values. This linter helps ensure all errors are properly handled.
  
- **exhaustive**: Checks if `switch` statements include all possible values of an enum type, ensuring exhaustiveness of code. This helps avoid forgetting to handle certain cases.

#### 10.4 Example: `errcheck`

**Incorrect Code Example**:
```go
package main

import (
    "fmt"
    "os"
)

func main() {
    f, _ := os.Open("filename.ext")
    defer f.Close()
}
```

**Issue**: In the above code, the error return value of `os.Open` function is explicitly ignored. This is a common mistake as it may lead to unhandled errors and hard-to-trace bugs.

**Correct Form**:
```go
package main

import (
    "fmt"
    "os"
)

func main() {
    f, err := os.Open("filename.ext")
    if err != nil {
        fmt.Printf("error opening file: %v\n", err)
        return
    }
    defer f.Close()
}
```

In the correct form, by checking the error (`err`) returned by `os.Open`, we gracefully handle error cases rather than simply ignoring them.

#### 10.5 Example: `gofmt`

**Incorrect Code Example**:
```go
package main
import "fmt"
func main() {
fmt.Println("Hello, world!")
}
```

**Issue**: This code snippet doesn't follow Go's standard formatting rules, for example, incorrect indentation of `fmt.Println`.

**Correct Form**:
```go
package main

import "fmt"

func main() {
    fmt.Println("Hello, world!")
}
```

Using `gofmt` tool can automatically fix such formatting issues, ensuring the code adheres to the coding standards of the Go community.

#### 10.6 Example: `unused`

**Incorrect Code Example**:
```go
package main

func helper() {}

func main() {}
```

**Issue**: The `helper` function is defined but not called anywhere, indicating potential redundant code or missing functionality implementation.

**Correct Form**:
```go
package main

// If the helper function is indeed needed, ensure it's used properly.
func helper() {
    // Implement the function's functionality or ensure it's called elsewhere
}

func main() {
    helper()
}
```

To improve the section on Linters settings in the document, we'll expand with more detailed explanations and reinforce understanding through examples.

#### 10.7 Example: `dogsled`

**Incorrect Code Example**:
```go
func getValues() (int, int, int) {
    return 1, 2, 3
}

func main() {
    _, _, val := getValues()
    fmt.Println(val) // Only interested in the third return value
}
```

**Explanation**: In the above code, we use two blank identifiers to ignore the first two return values. Excessive use of blank identifiers can make code reading difficult.

**Improved Code**:
Consider refactoring the function or the usage of return values to reduce the need for blank identifiers or explicitly comment why ignoring certain values is safe.

#### 10.8: `exhaustive`

**Incorrect Code Example**:
```go
type Fruit int

const (
    Apple Fruit = iota
    Banana
    Orange
)

func getFruitName(f Fruit) string {
    switch f {
    case Apple:
        return "Apple"
    case Banana:
        return "Banana"
    // Missing handling for Orange
    }
    return "Unknown"
}
```

**Explanation**: In this code, the `switch` statement doesn't cover all possible values of the `Fruit` type; the case for `Orange` is missing.

**Improved Code**:
```go
func getFruitName(f Fruit) string {
    switch f {
    case Apple:
        return "Apple"
    case Banana:
        return "Banana"
    case Orange:
        return "Orange"
    }
    return "Unknown"
}
```

By adding the missing `case`, we ensure the `switch` statement is exhaustive, handling every possible enum value.

#### 10.9 Optimization of Configuration Files and Application of Code Analysis Tools

Through these examples, we demonstrate how to improve code quality by identifying and fixing common coding issues. OpenIM's configuration files allow developers to customize linters' behavior according to project requirements, ensuring code compliance with predefined quality standards and style guidelines.

By employing these tools and configuration strategies, teams can reduce the number of bugs, enhance code maintainability, and facilitate efficient collaboration during code review processes.
