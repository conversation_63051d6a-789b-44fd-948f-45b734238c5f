# options for analysis running
run:
  # default concurrency is a available CPU number
  concurrency: 4

  # timeout for analysis, e.g. 30s, 5m, default is 1m
  timeout: 5m

  # exit code when at least one issue was found, default is 1
  issues-exit-code: 1

  # include test files or not, default is true
  tests: true

  # list of build tags, all linters use it. Default is empty list.
  build-tags:
    - mytag

  # which dirs to skip: issues from them won't be reported;
  # can use regexp here: generated.*, regexp is applied on full path;
  # default value is empty list, but default dirs are skipped independently
  # from this option's value (see skip-dirs-use-default).
  # "/" will be replaced by current OS file path separator to properly work
  # on Windows.
  # skip-dirs:
  #   - components
  #   - docs
  #   - util
  #   - .*~
  #   - api/swagger/docs

  
  #   - server/docs
  #   - components/mnt/config/certs
  #   - logs

  # default is true. Enables skipping of directories:
  #   vendor$, third_party$, testdata$, examples$, Godeps$, builtin$
  # skip-dirs-use-default: true

  # which files to skip: they will be analyzed, but issues from them
  # won't be reported. Default value is empty list, but there is
  # no need to include all autogenerated files, we confidently recognize
  # autogenerated files. If it's not please let us know.
  # "/" will be replaced by current OS file path separator to properly work
  # on Windows.
  # skip-files:
  #   - ".*\\.my\\.go$"
  #   - _test.go
  #   - ".*_test.go"
  #   - "mocks/"
  #   - ".github/"
  #   - "logs/"
  #   - "_output/"
  #   - "components/"

  # by default isn't set. If set we pass it to "go list -mod={option}". From "go help modules":
  # If invoked with -mod=readonly, the go command is disallowed from the implicit
  # automatic updating of go.mod described above. Instead, it fails when any changes
  # to go.mod are needed. This setting is most useful to check that go.mod does
  # not need updates, such as in a continuous integration and testing system.
  # If invoked with -mod=vendor, the go command assumes that the vendor
  # directory holds the correct copies of dependencies and ignores
  # the dependency descriptions in go.mod.
  #modules-download-mode: release|readonly|vendor

  # Allow multiple parallel golangci-lint instances running.
  # If false (default) - golangci-lint acquires file lock on start.
  allow-parallel-runners: true


# output configuration options
output:
  # colored-line-number|line-number|json|tab|checkstyle|code-climate, default is "colored-line-number"
  # format: colored-line-number

  # print lines of code with issue, default is true
  print-issued-lines: true

  # print linter name in the end of issue text, default is true
  print-linter-name: true

  # make issues output unique by line, default is true
  uniq-by-line: true

  # add a prefix to the output file references; default is no prefix
  path-prefix: ""

  # sorts results by: filepath, line and column
  sort-results: true

# all available settings of specific linters
linters-settings:
  bidichk:
    # The following configurations check for all mentioned invisible unicode
    # runes. It can be omitted because all runes are enabled by default.
    left-to-right-embedding: true
    right-to-left-embedding: true
    pop-directional-formatting: true
    left-to-right-override: true
    right-to-left-override: true
    left-to-right-isolate: true
    right-to-left-isolate: true
    first-strong-isolate: true
    pop-directional-isolate: true

  dupl:
    # tokens count to trigger issue, 150 by default
    threshold: 200
  errcheck:
    # report about not checking of errors in type assertions: `a := b.(MyStruct)`;
    # default is false: such cases aren't reported by default.
    check-type-assertions: false

    # report about assignment of errors to blank identifier: `num, _ := strconv.Atoi(numStr)`;
    # default is false: such cases aren't reported by default.
    check-blank: false

    # [deprecated] comma-separated list of pairs of the form pkg:regex
    # the regex is used to ignore names within pkg. (default "fmt:.*").
    # see https://github.com/kisielk/errcheck#the-deprecated-method for details
    #ignore: GenMarkdownTree,os:.*,BindPFlags,WriteTo,Help
    #ignore: (os\.)?std(out|err)\..*|.*Close|.*Flush|os\.Remove(All)?|.*print(f|ln)?|os\.(Un)?Setenv

    # path to a file containing a list of functions to exclude from checking
    # see https://github.com/kisielk/errcheck#excluding-functions for details
    # exclude: errcheck.txt

  errorlint:
    # Check whether fmt.Errorf uses the %w verb for formatting errors. See the readme for caveats
    errorf: true
    # Check for plain type assertions and type switches
    asserts: true
    # Check for plain error comparisons
    comparison: true

  exhaustive:
    # Program elements to check for exhaustiveness.
    # Default: [ switch ]
    check:
      - switch
      - map
    # check switch statements in generated files also
    check-generated: false
    # indicates that switch statements are to be considered exhaustive if a
    # 'default' case is present, even if all enum members aren't listed in the
    # switch
    default-signifies-exhaustive: false
    # enum members matching the supplied regex do not have to be listed in
    # switch statements to satisfy exhaustiveness
    ignore-enum-members: ""
    # consider enums only in package scopes, not in inner scopes
    package-scope-only: false


  forbidigo:
  #   # Forbid the following identifiers (identifiers are written using regexp):
    forbid:
      # - ^print.*$
      - 'fmt\.Print.*'
      - fmt.Println.* # too much log noise
      - ^unsafe\..*$
      - ^init$
      - ^os.Exit$
      - ^fmt.Print.*$
      - errors.New.*$
      - ^fmt.Println.*$
      - ^panic$
      - painc
  #     - ginkgo\\.F.* # these are used just for local development
  #   # Exclude godoc examples from forbidigo checks.  Default is true.
  #   exclude_godoc_examples: false

  funlen:
    lines: 220
    statements: 80

  gocognit:
    # minimal code complexity to report, 30 by default (but we recommend 10-20)
    min-complexity: 30

  goconst:
    # minimal length of string constant, 3 by default
    min-len: 3
    # minimal occurrences count to trigger, 3 by default
    min-occurrences: 3
    # ignore test files, false by default
    ignore-tests: false
    # look for existing constants matching the values, true by default
    match-constant: true
    # search also for duplicated numbers, false by default
    numbers: false
    # minimum value, only works with goconst.numbers, 3 by default
    min: 3
    # maximum value, only works with goconst.numbers, 3 by default
    max: 3
    # ignore when constant is not used as function argument, true by default
    ignore-calls: true

  gocritic:
    # Which checks should be enabled; can't be combined with 'disabled-checks';
    # See https://go-critic.github.io/overview#checks-overview
    # To check which checks are enabled run `GL_DEBUG=gocritic golangci-lint run`
    # By default list of stable checks is used.
    enabled-checks:
      #- rangeValCopy
      - ruleguard

    # Which checks should be disabled; can't be combined with 'enabled-checks'; default is empty
    disabled-checks:
      - regexpMust
      - ifElseChain
      #- exitAfterDefer

    # Enable multiple checks by tags, run `GL_DEBUG=gocritic golangci-lint run` to see all tags and checks.
    # Empty list by default. See https://github.com/go-critic/go-critic#usage -> section "Tags".
    enabled-tags:
      - performance
    disabled-tags:
      - experimental

    # Settings passed to gocritic.
    # The settings key is the name of a supported gocritic checker.
    # The list of supported checkers can be find in https://go-critic.github.io/overview.
    settings:
      captLocal: # must be valid enabled check name
        # whether to restrict checker to params only (default true)
        paramsOnly: true
      elseif:
        # whether to skip balanced if-else pairs (default true)
        skipBalanced: true
      hugeParam:
        # size in bytes that makes the warning trigger (default 80)
        sizeThreshold: 80
      rangeExprCopy:
        # size in bytes that makes the warning trigger (default 512)
        sizeThreshold: 512
        # whether to check test functions (default true)
        skipTestFuncs: true
      rangeValCopy:
        # size in bytes that makes the warning trigger (default 128)
        sizeThreshold: 32
        # whether to check test functions (default true)
        skipTestFuncs: true
      ruleguard:
        # path to a gorules file for the ruleguard checker
        rules: ''
      underef:
        # whether to skip (*x).method() calls where x is a pointer receiver (default true)
        skipRecvDeref: true

  gocyclo:
    # minimal code complexity to report, 30 by default (but we recommend 10-20)
    min-complexity: 30
  cyclop:
    # the maximal code complexity to report
    max-complexity: 50
    # the maximal average package complexity. If it's higher than 0.0 (float) the check is enabled (default 0.0)
    package-average: 0.0
    # should ignore tests (default false)
    skip-tests: false
  godot:
    # comments to be checked: `declarations`, `toplevel`, or `all`
    scope: declarations
    # list of regexps for excluding particular comment lines from check
    exclude:
      # example: exclude comments which contain numbers
      - '[0-9]+'
      - 'func\s+\w+'
      - 'FIXME:'
      - '.*func.*'
    # check that each sentence starts with a capital letter
    capital: true
  godox:
    # report any comments starting with keywords, this is useful for TODO or FIXME comments that
    # might be left in the code accidentally and should be resolved before merging
    keywords: # default keywords are TODO, BUG, and FIXME, these can be overwritten by this setting
      #- TODO
      - BUG
      - FIXME
      #- NOTE
      - OPTIMIZE # marks code that should be optimized before merging
      - HACK # marks hack-arounds that should be removed before merging
  gofmt:
    # simplify code: gofmt with `-s` option, true by default
    simplify: true

  gofumpt:
    # Select the Go version to target. The default is `1.18`.
    go-version: "1.21"

    # Choose whether or not to use the extra rules that are disabled
    # by default
    extra-rules: false

  # goheader:
    # values:
      # const:
        # define here const type values in format k:v, for example:
        # COMPANY: MY COMPANY
      # regexp:
        # define here regexp type values, for example
        # AUTHOR: .*@mycompany\.com
    # template: # |-
      # put here copyright header template for source code files, for example:
      # Note: {{ YEAR }} is a builtin value that returns the year relative to the current machine time.
      #
      # {{ AUTHOR }} {{ COMPANY }} {{ YEAR }}
      # SPDX-License-Identifier: Apache-2.0

      # Licensed under the Apache License, Version 2.0 (the "License");
      # you may not use this file except in compliance with the License.
      # You may obtain a copy of the License at:

      #   http://www.apache.org/licenses/LICENSE-2.0

      # Unless required by applicable law or agreed to in writing, software
      # distributed under the License is distributed on an "AS IS" BASIS,
      # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
      # See the License for the specific language governing permissions and
      # limitations under the License.
    # template-path:
      # also as alternative of directive 'template' you may put the path to file with the template source

  goimports:
    # put imports beginning with prefix after 3rd-party packages;
    # it's a comma-separated list of prefixes
    local-prefixes: github.com/openimsdk/open-im-server

  gomnd:
    # List of enabled checks, see https://github.com/tommy-muehle/go-mnd/#checks for description.
    # Default: ["argument", "case", "condition", "operation", "return", "assign"]
    checks:
      - argument
      - case
      - condition
      - operation
      - return
      - assign
    # List of numbers to exclude from analysis.
    # The numbers should be written as string.
    # Values always ignored: "1", "1.0", "0" and "0.0"
    # Default: []
    ignored-numbers:
      - '0666'
      - '0755'
      - '42'
    # List of file patterns to exclude from analysis.
    # Values always ignored: `.+_test.go`
    # Default: []
    ignored-files:
      - 'magic1_.+\.go$'
    # List of function patterns to exclude from analysis.
    # Following functions are always ignored: `time.Date`,
    # `strconv.FormatInt`, `strconv.FormatUint`, `strconv.FormatFloat`,
    # `strconv.ParseInt`, `strconv.ParseUint`, `strconv.ParseFloat`.
    # Default: []
    ignored-functions:
      - '^math\.'
      - '^webhook\.StatusText$'
  gomoddirectives:
    # Allow local `replace` directives. Default is false.
    replace-local: true
    # List of allowed `replace` directives. Default is empty.
    replace-allow-list:
      - google.golang.org/grpc

    # Allow to not explain why the version has been retracted in the `retract` directives. Default is false.
    retract-allow-no-explanation: false
    # Forbid the use of the `exclude` directives. Default is false.
    exclude-forbidden: false

  gomodguard:
    allowed:
      modules:    
        - gorm.io/gen                                                    # List of allowed modules
        - gorm.io/gorm
        - gorm.io/driver/mysql
        - k8s.io/klog
        - github.com/allowed/module
        - go.mongodb.org/mongo-driver/mongo
        # - gopkg.in/yaml.v2
      domains:                                                        # List of allowed module domains
        - google.golang.org
        - gopkg.in
        - golang.org
        - github.com
        - go.mongodb.org
        - go.uber.org
        - openim.io
        - go.etcd.io
    blocked:
      versions:
        - github.com/MakeNowJust/heredoc:
            version: "> 2.0.9"
            reason: "use the latest version"
      local_replace_directives: false     # Set to true to raise lint issues for packages that are loaded from a local path via replace directive

  gosec:
    # To select a subset of rules to run.
    # Available rules: https://github.com/securego/gosec#available-rules
    includes:
      - G401
      - G306
      - G101
    # To specify a set of rules to explicitly exclude.
    # Available rules: https://github.com/securego/gosec#available-rules
    excludes:
      - G204
    # Exclude generated files
    exclude-generated: true
    # Filter out the issues with a lower severity than the given value. Valid options are: low, medium, high.
    severity: "low"
    # Filter out the issues with a lower confidence than the given value. Valid options are: low, medium, high.
    confidence: "low"
    # To specify the configuration of rules.
    # The configuration of rules is not fully documented by gosec:
    # https://github.com/securego/gosec#configuration
    # https://github.com/securego/gosec/blob/569328eade2ccbad4ce2d0f21ee158ab5356a5cf/rules/rulelist.go#L60-L102
    config:
      G306: "0600"
      G101:
        pattern: "(?i)example"
        ignore_entropy: false
        entropy_threshold: "80.0"
        per_char_threshold: "3.0"
        truncate: "32"

  gosimple:
    # Select the Go version to target. The default is '1.13'.
    go: "1.20"
    # https://staticcheck.io/docs/options#checks
    checks: [ "all" ]

  govet:
    # settings per analyzer
    settings:
      printf: # analyzer name, run `go tool vet help` to see all analyzers
        funcs: # run `go tool vet help printf` to see available settings for `printf` analyzer
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Infof
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Warnf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Errorf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Fatalf

    # enable or disable analyzers by name
    enable:
      - atomicalign
    enable-all: false
    disable:
      - shadow
    disable-all: false

  depguard:
    rules:
      prevent_unmaintained_packages:
        list-mode: lax # allow unless explicitely denied
        files:
          - $all
          - "!$test"
        allow:
          - $gostd
        deny:
          - pkg: io/ioutil
            desc: "replaced by io and os packages since Go 1.16: https://tip.golang.org/doc/go1.16#ioutil"
          - pkg: github.com/OpenIMSDK
            desc: "The OpenIM organization has been replaced with lowercase, please do not use uppercase organization name, you will use openimsdk"
          - pkg: log
            desc: "We have a wrapped log package at openim, we recommend you to use our wrapped log package, https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/logging.md"
          - pkg: errors
            desc: "We have a wrapped errors package at openim, we recommend you to use our wrapped errors package, https://github.com/openimsdk/open-im-server/blob/main/docs/contrib/error-code.md"

  importas:
    # if set to `true`, force to use alias.
    no-unaliased: true
    # List of aliases
    alias:
      # using `servingv1` alias for `knative.dev/serving/pkg/apis/serving/v1` package
      - pkg: knative.dev/serving/pkg/apis/serving/v1
        alias: servingv1
      - pkg: gopkg.in/yaml.v2
        alias: yaml
      # using `autoscalingv1alpha1` alias for `knative.dev/serving/pkg/apis/autoscaling/v1alpha1` package
      - pkg: knative.dev/serving/pkg/apis/autoscaling/v1alpha1
        alias: autoscalingv1alpha1
      # You can specify the package path by regular expression,
      # and alias by regular expression expansion syntax like below.
      # see https://github.com/julz/importas#use-regular-expression for details
      - pkg: knative.dev/serving/pkg/apis/(\w+)/(v[\w\d]+)
        alias: $1$2

  ireturn:
    # ireturn allows using `allow` and `reject` settings at the same time.
    # Both settings are lists of the keywords and regular expressions matched to interface or package names.
    # keywords:
    # - `empty` for `interface{}`
    # - `error` for errors
    # - `stdlib` for standard library
    # - `anon` for anonymous interfaces

    # By default, it allows using errors, empty interfaces, anonymous interfaces,
    # and interfaces provided by the standard library.
    allow:
    - anon
    - error
    - empty
    - stdlib
    # You can specify idiomatic endings for interface
    - (or|er)$

    # Reject patterns
    reject:
    - github.com\/user\/package\/v4\.Type

  lll:
    # max line length, lines longer will be reported. Default is 250.
    # '\t' is counted as 1 character by default, and can be changed with the tab-width option
    line-length: 250
    # tab width in spaces. Default to 1.
    tab-width: 4
  misspell:
    # Correct spellings using locale preferences for US or UK.
    # Default is to use a neutral variety of English.
    # Setting locale to US will correct the British spelling of 'colour' to 'color'.
    locale: US
    ignore-words:
      - someword
  nakedret:
    # make an issue if func has more lines of code than this setting and it has naked returns; default is 30
    max-func-lines: 30

  nestif:
    # minimal complexity of if statements to report, 5 by default
    min-complexity: 4

  nilnil:
    # By default, nilnil checks all returned types below.
    checked-types:
      - ptr
      - func
      - iface
      - map
      - chan

  nlreturn:
    # size of the block (including return statement that is still "OK")
    # so no return split required.
    block-size: 1

  nolintlint:
    # Disable to ensure that all nolint directives actually have an effect. Default is true.
    allow-unused: false
    # Exclude following linters from requiring an explanation.  Default is [].
    allow-no-explanation: [ ]
    # Enable to require an explanation of nonzero length after each nolint directive. Default is false.
    require-explanation: false
    # Enable to require nolint directives to mention the specific linter being suppressed. Default is false.
    require-specific: true

  prealloc:
    # XXX: we don't recommend using this linter before doing performance profiling.
    # For most programs usage of prealloc will be a premature optimization.

    # Report preallocation suggestions only on simple loops that have no returns/breaks/continues/gotos in them.
    # True by default.
    simple: true
    range-loops: true # Report preallocation suggestions on range loops, true by default
    for-loops: false # Report preallocation suggestions on for loops, false by default

  promlinter:
    # Promlinter cannot infer all metrics name in static analysis.
    # Enable strict mode will also include the errors caused by failing to parse the args.
    strict: false
    # Please refer to https://github.com/yeya24/promlinter#usage for detailed usage.
    disabled-linters:
     - "Help"
     - "MetricUnits"
     - "Counter"
     - "HistogramSummaryReserved"
     - "MetricTypeInName"
     - "ReservedChars"
     - "CamelCase"
    
  predeclared:
    # comma-separated list of predeclared identifiers to not report on
    ignore: ""
    # include method names and field names (i.e., qualified names) in checks
    q: false
  rowserrcheck:
    packages:
      - github.com/jmoiron/sqlx

  revive:
    # see https://github.com/mgechev/revive#available-rules for details.
    ignore-generated-header: true
    severity: warning
    rules:
      - name: indent-error-flow
        severity: warning
      - name: exported
        severity: warning
      - name: var-naming
        arguments: [ [ "OpenIM"] ]
        # arguments: [ ["ID", "HTTP", "URL", "URI", "API", "APIKey", "Token", "TokenID", "TokenSecret", "TokenKey", "TokenSecret", "JWT", "JWTToken", "JWTTokenID", "JWTTokenSecret", "JWTTokenKey", "JWTTokenSecret", "OAuth", "OAuthToken", "RPC" ] ]
      - name: atomic
      - name: line-length-limit
        severity: error
        arguments: [200]
      - name: unhandled-error
        arguments : ["fmt.Printf", "myFunction"]

  staticcheck:
    # Select the Go version to target. The default is '1.13'.
    go: "1.20"
    # https://staticcheck.io/docs/options#checks
    checks: [ "all" ]

  stylecheck:
    # Select the Go version to target. The default is '1.13'.
    go: "1.20"

    # https://staticcheck.io/docs/options#checks
    checks: [ "all", "-ST1000", "-ST1003", "-ST1016", "-ST1020", "-ST1021", "-ST1022" ]
    # https://staticcheck.io/docs/options#dot_import_whitelist
    dot-import-whitelist:
      - fmt
    # https://staticcheck.io/docs/options#initialisms
    initialisms: [ "ACL", "API", "ASCII", "CPU", "CSS", "DNS", "EOF", "GUID", "HTML", "HTTP", "HTTPS", "ID", "IP", "JSON", "QPS", "RAM", "RPC", "SLA", "SMTP", "SQL", "SSH", "TCP", "TLS", "TTL", "UDP", "UI", "GID", "UID", "UUID", "URI", "URL", "UTF8", "VM", "XML", "XMPP", "XSRF", "XSS" ]
    # https://staticcheck.io/docs/options#http_status_code_whitelist
    http-status-code-whitelist: [ "200", "400", "404", "500" ]

  tagliatelle:
    # check the struck tag name case
    case:
      # use the struct field name to check the name of the struct tag
      use-field-name: true
      rules:
        # any struct tag type can be used.
        # support string case: `camel`, `pascal`, `kebab`, `snake`, `goCamel`, `goPascal`, `goKebab`, `goSnake`, `upper`, `lower`
        json: camel
        yaml: camel
        xml: camel
        bson: camel
        avro: snake
        mapstructure: kebab

  testpackage:
    # regexp pattern to skip files
    skip-regexp: (id|export|internal)_test\.go
  thelper:
    # The following configurations enable all checks. It can be omitted because all checks are enabled by default.
    # You can enable only required checks deleting unnecessary checks.
    test:
      first: true
      name: true
      begin: true
    benchmark:
      first: true
      name: true
      begin: true
    tb:
      first: true
      name: true
      begin: true

  tenv:
    # The option `all` will run against whole test files (`_test.go`) regardless of method/function signatures.
    # By default, only methods that take `*testing.T`, `*testing.B`, and `testing.TB` as arguments are checked.
    all: false

  unparam:
    # Inspect exported functions, default is false. Set to true if no external program/library imports your code.
    # XXX: if you enable this setting, unparam will report a lot of false-positives in text editors:
    # if it's called for subdir of a project it can't find external interfaces. All text editor integrations
    # with golangci-lint call it on a directory with the changed file.
    check-exported: false
  # unused:
    # treat code as a program (not a library) and report unused exported identifiers; default is false.
    # XXX: if you enable this setting, unused will report a lot of false-positives in text editors:
    # if it's called for subdir of a project it can't find funcs usages. All text editor integrations
    # with golangci-lint call it on a directory with the changed file.
  whitespace:
    multi-if: false   # Enforces newlines (or comments) after every multi-line if statement
    multi-func: false # Enforces newlines (or comments) after every multi-line function signature

  wrapcheck:
    # An array of strings that specify substrings of signatures to ignore.
    # If this set, it will override the default set of ignored signatures.
    # See https://github.com/tomarrell/wrapcheck#configuration for more information.
    ignoreSigs:
      - .Errorf(
      - errors.New(
      - errors.Unwrap(
      - .Wrap(
      - .WrapMsg(
      - .Wrapf(
      - .WithMessage(
      - .WithMessagef(
      - .WithStack(
    ignorePackageGlobs:
        - encoding/*
        - github.com/pkg/*
        - github.com/openimsdk/*
        - github.com/OpenIMSDK/*

  wsl:
    # If true append is only allowed to be cuddled if appending value is
    # matching variables, fields or types on line above. Default is true.
    strict-append: true
    # Allow calls and assignments to be cuddled as long as the lines have any
    # matching variables, fields or types. Default is true.
    allow-assign-and-call: true
    # Allow assignments to be cuddled with anything. Default is false.
    allow-assign-and-anything: false
    # Allow multiline assignments to be cuddled. Default is true.
    allow-multiline-assign: true
    # Allow declarations (var) to be cuddled.
    allow-cuddle-declarations: false
    # Allow trailing comments in ending of blocks
    allow-trailing-comment: false
    # Force newlines in end of case at this limit (0 = never).
    force-case-trailing-whitespace: 0
    # Force cuddling of err checks with err var assignment
    force-err-cuddling: false
    # Allow leading comments to be separated with empty liens
    allow-separated-leading-comment: false
  makezero:
    # Allow only slices initialized with a length of zero. Default is false.
    always: false

  # The custom section can be used to define linter plugins to be loaded at runtime. See README doc
  #  for more info.
  #custom:
    # Each custom linter should have a unique name.
    #example:
      # The path to the plugin *.so. Can be absolute or local. Required for each custom linter
      #path: /path/to/example.so
      # The description of the linter. Optional, just for documentation purposes.
      #description: This is an example usage of a plugin linter.
      # Intended to point to the repo location of the linter. Optional, just for documentation purposes.
      #original-url: github.com/golangci/example-linter

linters:
  # please, do not use `enable-all`: it's deprecated and will be removed soon.
  # inverted configuration with `enable-all` and `disable` is not scalable during updates of golangci-lint
  # enable-all: true
  disable-all: true
  enable:
    - typecheck     # Basic type checking
    - gofmt         # Format check
    - govet         # Go's standard linting tool
    - gosimple      # Suggestions for simplifying code
    - errcheck
    - decorder
    - ineffassign
    - forbidigo
    - revive
    - reassign
    - tparallel
    - unconvert
    - fieldalignment
    - dupl
    - dupword
    - errname
    - gci
    - exhaustive
    - gocritic
    - goprintffuncname
    - gomnd
    - goconst
    - gosec
    - misspell      # Spelling mistakes
    - staticcheck   # Static analysis
    - unused        # Checks for unused code
    # - goimports     # Checks if imports are correctly sorted and formatted
    - godot         # Checks for comment punctuation
    - bodyclose     # Ensures HTTP response body is closed
    - stylecheck    # Style checker for Go code
    - unused        # Checks for unused code
    - errcheck      # Checks for missed error returns
  fast: true

issues:
  # List of regexps of issue texts to exclude, empty list by default.
  # But independently from this option we use default exclude patterns,
  # it can be disabled by `exclude-use-default: false`. To list all
  # excluded by default patterns execute `golangci-lint run --help`
  exclude:
    - tools/.*
    - test/.*
    - components/*
    - third_party/.*

  # Excluding configuration per-path, per-linter, per-text and per-source
  exclude-rules:
    - linters:
      - revive
      path: (log/.*)\.go

    - linters:
      - wrapcheck
      path: (cmd/.*|pkg/.*)\.go

    - linters:
      - typecheck
        #path: (pkg/storage/.*)\.go
      path: (internal/.*|pkg/.*)\.go

    - path: (cmd/.*|test/.*|tools/.*|internal/pump/pumps/.*)\.go
      linters:
        - forbidigo

    - path: (cmd/[a-z]*/.*|store/.*)\.go
      linters:
        - dupl

    - linters:
        - gocritic
      text: (hugeParam:|rangeValCopy:)

    - path: (cmd/[a-z]*/.*)\.go
      linters:
        - lll

    - path: (validator/.*|code/.*|validator/.*|watcher/watcher/.*)
      linters:
        - gochecknoinits

    - path: (internal/.*/options|internal/pump|pkg/log/options.go|internal/authzserver|tools/)
      linters:
        - tagliatelle

    - path: (pkg/app/.*)\.go
      linters:
        - unused
        - forbidigo

    # Exclude some staticcheck messages
    - linters:
        - staticcheck
      text: "SA9003:"

    # Exclude lll issues for long lines with go:generate
    - linters:
        - lll
      source: "^//go:generate "

    - text: ".*[\u4e00-\u9fa5]+.*"
      linters:
        - golint
      source: "^//.*$"

  # Independently from option `exclude` we use default exclude patterns,
  # it can be disabled by this option. To list all
  # excluded by default patterns execute `golangci-lint run --help`.
  # Default value for this option is true.
  exclude-use-default: true

  # The default value is false. If set to true exclude and exclude-rules
  # regular expressions become case sensitive.
  exclude-case-sensitive: false

  # The list of ids of default excludes to include or disable. By default it's empty.
  include:
    - EXC0002 # disable excluding of issues about comments from golint

  # Maximum issues count per one linter. Set to 0 to disable. Default is 50.
  max-issues-per-linter: 0

  # Maximum count of issues with the same text. Set to 0 to disable. Default is 3.
  max-same-issues: 0

  # Show only new issues: if there are unstaged changes or untracked files,
  # only those changes are analyzed, else only changes in HEAD~ are analyzed.
  # It's a super-useful option for integration of golangci-lint into existing
  # large codebase. It's not practical to fix all existing issues at the moment
  # of integration: much better don't allow issues in new code.
  # Default is false.
  new: false

  # Show only new issues created after git revision `REV`
  # new-from-rev: REV

  # Show only new issues created in git patch with set file path.
  #new-from-patch: path/to/patch/file

  # Fix found issues (if it's supported by the linter)
  fix: true

severity:
  # Default value is empty string.
  # Set the default severity for issues. If severity rules are defined and the issues
  # do not match or no severity is provided to the rule this will be the default
  # severity applied. Severities should match the supported severity names of the
  # selected out format.
  # - Code climate: https://docs.codeclimate.com/docs/issues#issue-severity
  # -   Checkstyle: https://checkstyle.sourceforge.io/property_types.html#severity
  # -       Github: https://help.github.com/en/actions/reference/workflow-commands-for-github-actions#setting-an-error-message
  default-severity: error

  # The default value is false.
  # If set to true severity-rules regular expressions become case sensitive.
  case-sensitive: false

  # Default value is empty list.
  # When a list of severity rules are provided, severity information will be added to lint
  # issues. Severity rules have the same filtering capability as exclude rules except you
  # are allowed to specify one matcher per severity rule.
  # Only affects out formats that support setting severity information.
  rules:
    - linters:
      - dupl
      severity: info
